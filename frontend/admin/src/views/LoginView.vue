<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
    <div class="max-w-md w-full bg-white rounded-2xl shadow-xl p-8">
      <div class="text-center mb-8">
        <div class="mx-auto h-12 w-12 bg-blue-600 rounded-lg flex items-center justify-center mb-4">
          <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
          </svg>
        </div>
        <h2 class="text-2xl font-bold text-gray-900">智能评估平台</h2>
        <p class="text-gray-600 mt-2">安全登录系统</p>
      </div>

      <!-- 登录表单 -->
      <el-form :model="loginForm" :rules="rules" ref="loginFormRef" @submit.prevent="handleLogin" class="space-y-4">
        <el-form-item prop="username">
          <label class="block text-sm font-medium text-gray-700 mb-2">用户名</label>
          <el-input
            v-model="loginForm.username"
            placeholder="请输入用户名，如：zhangsan.001"
            size="large"
            prefix-icon="User"
            clearable
          />
          <div class="text-xs text-gray-500 mt-1">
            格式：姓名拼音.工号 (如：zhangsan.001)
          </div>
        </el-form-item>
        
        <el-form-item prop="tenantCode">
          <label class="block text-sm font-medium text-gray-700 mb-2">机构代码</label>
          <el-input
            v-model="loginForm.tenantCode"
            placeholder="请输入机构代码，如：SH02YL01"
            size="large"
            prefix-icon="Building"
            clearable
            style="text-transform: uppercase"
            @input="handleTenantCodeInput"
          />
          <!-- 智能提示 -->
          <div v-if="tenantCodeHint" class="text-xs mt-1" :class="{
            'text-green-600': tenantCodeHint.startsWith('✓'),
            'text-blue-600': tenantCodeHint.startsWith('💡'),
            'text-red-600': tenantCodeHint.startsWith('⚠️')
          }">
            {{ tenantCodeHint }}
          </div>
          <!-- 静态帮助信息 -->
          <div v-else class="text-xs text-gray-500 mt-1">
            <div>省级：SH01MZ (上海民政厅) · 市级：SH02MZ (浦东民政局)</div>
            <div>机构：SH02YL01 (浦东阳光养老院) · 系统：SYSTEM</div>
          </div>
        </el-form-item>
        
        <el-form-item prop="password">
          <label class="block text-sm font-medium text-gray-700 mb-2">密码</label>
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            size="large"
            prefix-icon="Lock"
            show-password
            @keyup.enter="handleLogin"
          />
        </el-form-item>
        
        <!-- 滑动验证码区域 - 直接使用共享组件 -->
        <div class="captcha-section">
          <div class="captcha-status-info" style="margin-bottom: 15px; padding: 8px; background: #f8f9fa; border-radius: 4px; border-left: 4px solid #007bff;">
            <p style="font-size: 12px; color: #666; margin: 0;">
              🔍 验证状态: {{ captchaVerified ? '✅ 已通过验证' : '⏳ 请拖动下方滑块，使拼图块对准缺口位置' }}
            </p>
            <p v-if="isDragging" style="font-size: 11px; color: #28a745; margin: 2px 0 0 0;">
              🎯 前端位置: {{ calculateJigsawPosition() }}px | 后端坐标: {{ Math.round(50 + (sliderPosition / 264) * (310 - 47 - 50)) }}px
            </p>
          </div>
          
          <!-- 简化的内联验证码组件 -->
          <div class="inline-captcha-container">
            <div v-if="captchaLoading" style="text-align: center; padding: 20px; color: #666;">
              🔄 正在加载验证码...
            </div>
            
            <div v-else-if="captchaData.originalImageBase64" class="captcha-display">
              <!-- 背景图片容器 -->
              <div class="captcha-image-container">
                <img 
                  :src="'data:image/png;base64,' + captchaData.originalImageBase64"
                  class="captcha-bg-image"
                  draggable="false"
                />
                
                <!-- 滑块图片 -->
                <img 
                  v-if="captchaData.jigsawImageBase64"
                  :src="'data:image/png;base64,' + captchaData.jigsawImageBase64"
                  class="captcha-block-image"
                  :style="{ 
                    left: calculateJigsawPosition() + 'px',
                    opacity: isDragging ? 0.8 : 1
                  }"
                  draggable="false"
                />
                
                <!-- 刷新按钮 -->
                <div class="captcha-refresh-btn" @click="refreshCaptcha">
                  <span>🔄</span>
                </div>
              </div>
              
              <!-- 滑动条 -->
              <div class="captcha-slider" ref="sliderContainer">
                <div class="slider-track">
                  <!-- 背景提示文字 -->
                  <div class="slider-track-bg">
                    <span class="slider-text">{{ sliderVerified ? '验证成功' : '拖动滑块完成验证' }}</span>
                  </div>
                  
                  <!-- 已滑动的绿色区域 -->
                  <div 
                    class="slider-fill" 
                    :style="{ 
                      width: Math.max(0, sliderPosition) + 'px',
                      opacity: sliderPosition > 10 ? 1 : 0
                    }"
                  >
                  </div>
                </div>
                
                <!-- 滑块按钮 - 简化结构 -->
                <div 
                  ref="sliderButton"
                  class="slider-button"
                  :class="{ 'slider-verified': sliderVerified, 'slider-dragging': isDragging }"
                  :style="{ 
                    left: sliderPosition + 'px',
                    transform: isDragging ? 'scale(1.1)' : 'scale(1)'
                  }"
                  @mousedown="handleMouseDown"
                  @touchstart="handleTouchStart"
                >
                  <span class="slider-icon">{{ sliderVerified ? '✓' : '≫' }}</span>
                </div>
              </div>
            </div>
            
            <div v-else style="text-align: center; padding: 20px; color: #999;">
              ❌ 验证码加载失败
              <button 
                type="button" 
                @click="loadCaptcha" 
                style="margin-left: 10px; padding: 5px 10px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; font-size: 12px;"
              >
                重新加载
              </button>
            </div>
          </div>
        </div>
        
        <el-button
          type="primary"
          size="large"
          :loading="loading"
          :disabled="loading || !captchaVerified"
          @click="handleLogin"
          class="w-full"
        >
          <span v-if="loading">登录中...</span>
          <span v-else-if="!captchaVerified">请先完成验证码</span>
          <span v-else>安全登录</span>
        </el-button>
      </el-form>

      <!-- 帮助链接 -->
      <div class="mt-6 text-center">
        <el-button link @click="showHelpDialog = true" class="text-sm text-gray-500">
          登录遇到问题？
        </el-button>
      </div>
    </div>

    <!-- 帮助对话框 -->
    <el-dialog v-model="showHelpDialog" title="登录帮助" width="400px">
      <div class="space-y-4 text-sm">
        <div>
          <h4 class="font-medium text-gray-900 mb-2">用户名格式：</h4>
          <ul class="list-disc list-inside text-gray-600 space-y-1">
            <li>姓名拼音.工号：zhangsan.001</li>
            <li>姓名拼音.部门：lisi.asm (assessor)</li>
            <li>姓名拼音.角色：wangwu.admin</li>
          </ul>
        </div>
        
        <div>
          <h4 class="font-medium text-gray-900 mb-2">机构代码规则：</h4>
          <ul class="list-disc list-inside text-gray-600 space-y-1">
            <li>省级：SH01MZ (上海01民政)</li>
            <li>市级：SH02MZ (上海02民政)</li>
            <li>机构：SH02YL01 (上海02养老01)</li>
            <li>系统：SYSTEM (超级管理员)</li>
          </ul>
        </div>
        
        <div>
          <h4 class="font-medium text-gray-900 mb-2">常见问题：</h4>
          <ul class="list-disc list-inside text-gray-600 space-y-1">
            <li>忘记机构代码：联系机构管理员</li>
            <li>用户名错误：检查拼音和工号格式</li>
            <li>无法登录：确认账号状态和权限</li>
          </ul>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="showHelpDialog = false">知道了</el-button>
      </template>
    </el-dialog>

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { useRouter } from 'vue-router';
import request from '@/utils/request';
import { getCaptcha, checkCaptcha } from '@/api/captcha';

const router = useRouter();
const loading = ref(false);
const loginFormRef = ref();
const showHelpDialog = ref(false);
const sharedCaptchaRef = ref();
const sliderContainer = ref();
const sliderButton = ref();

// 验证码相关
const captchaToken = ref('');
const captchaVerification = ref('');
const captchaVerified = ref(false);

// 内联验证码状态
const captchaLoading = ref(false);
const captchaData = reactive({
  token: '',
  originalImageBase64: '',
  jigsawImageBase64: '',
  y: 0
});
const sliderPosition = ref(2);
const sliderVerified = ref(false);
const isDragging = ref(false);
// 移除不需要的变量

onMounted(async () => {
  console.log('📱 Admin LoginView 已挂载，开始加载验证码...');
  await loadCaptcha();
  
  // 测试滑块是否可以点击
  setTimeout(() => {
    const sliderBtn = document.querySelector('.slider-button');
    if (sliderBtn) {
      console.log('✅ 滑块按钮找到:', sliderBtn);
      sliderBtn.addEventListener('click', () => {
        console.log('🖱️ 滑块按钮被点击了!');
      });
    } else {
      console.log('❌ 未找到滑块按钮');
    }
  }, 1000);
});

const loginForm = reactive({
  username: '',
  tenantCode: '',
  password: '',
});

const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { pattern: /^[a-zA-Z]+\.[a-zA-Z0-9]+$/, message: '用户名格式：姓名拼音.工号，如：zhangsan.001', trigger: 'blur' }
  ],
  tenantCode: [
    { required: true, message: '请输入机构代码', trigger: 'blur' },
    { min: 3, message: '机构代码至少3位', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' },
  ],
};

// 机构代码映射表类型定义
interface TenantInfo {
  name: string;
  type: string;
  level: string;
}

// 机构代码映射表（用于验证和提示）
const tenantCodeMap: Record<string, TenantInfo> = {
  // 系统管理员
  'SYSTEM': { name: '系统平台', type: 'PLATFORM', level: '系统级' },
  'MAINT': { name: '系统维护', type: 'MAINTENANCE', level: '系统级' },
  
  // 省级机构
  'SH01MZ': { name: '上海市民政厅', type: 'GOVERNMENT', level: '省级' },
  'BJ01MZ': { name: '北京市民政厅', type: 'GOVERNMENT', level: '省级' },
  'GD01MZ': { name: '广东省民政厅', type: 'GOVERNMENT', level: '省级' },
  
  // 市级机构
  'SH02MZ': { name: '上海市浦东新区民政局', type: 'GOVERNMENT', level: '市级' },
  'SH03MZ': { name: '上海市徐汇区民政局', type: 'GOVERNMENT', level: '市级' },
  'BJ02MZ': { name: '北京市朝阳区民政局', type: 'GOVERNMENT', level: '市级' },
  
  // 养老机构
  'SH02YL01': { name: '上海浦东阳光养老院', type: 'NURSING_HOME', level: '机构级' },
  'SH02YL02': { name: '上海浦东康复养老院', type: 'NURSING_HOME', level: '机构级' },
  'SH03YL01': { name: '上海徐汇长者之家', type: 'NURSING_HOME', level: '机构级' },
  'BJ02YL01': { name: '北京朝阳康养中心', type: 'NURSING_HOME', level: '机构级' },
  
  // 医疗机构
  'SH02YY01': { name: '上海浦东人民医院', type: 'HOSPITAL', level: '机构级' },
  'BJ02YY01': { name: '北京朝阳医院', type: 'HOSPITAL', level: '机构级' },
  
  // 兼容原有演示代码
  'platform': { name: '系统平台', type: 'PLATFORM', level: '系统级' },
  'demo_hospital': { name: '演示医院', type: 'HOSPITAL', level: '机构级' },
  'gov_province_civil': { name: '省民政厅', type: 'GOVERNMENT', level: '省级' },
  'gov_city_a_civil': { name: '市民政局A', type: 'GOVERNMENT', level: '市级' },
  'nursing_home_a1': { name: '阳光养老院', type: 'NURSING_HOME', level: '机构级' },
};

// 机构代码提示信息
const tenantCodeHint = ref('');

// 处理机构代码输入（自动转换为大写并提供提示）
const handleTenantCodeInput = (value: string) => {
  const upperValue = value.toUpperCase();
  loginForm.tenantCode = upperValue;
  
  // 提供智能提示
  if (upperValue.length >= 2) {
    const matchedTenant = tenantCodeMap[upperValue];
    if (matchedTenant) {
      tenantCodeHint.value = `✓ ${matchedTenant.name} (${matchedTenant.level})`;
    } else {
      // 模糊匹配提示
      const suggestions = Object.keys(tenantCodeMap).filter(code => 
        code.startsWith(upperValue)
      ).slice(0, 3);
      
      if (suggestions.length > 0) {
        const suggestionText = suggestions.map(code => 
          `${code} (${tenantCodeMap[code].name})`
        ).join(', ');
        tenantCodeHint.value = `💡 建议: ${suggestionText}`;
      } else {
        tenantCodeHint.value = '⚠️ 机构代码不存在，请检查输入';
      }
    }
  } else {
    tenantCodeHint.value = '';
  }
};

// 登录处理
const handleLogin = async () => {
  if (!loginFormRef.value) return;

  try {
    const valid = await loginFormRef.value.validate();
    if (!valid) return;

    loading.value = true;

    // 准备登录数据
    const loginData: any = {
      ...loginForm,
      captchaToken: captchaToken.value,
      captchaVerification: captchaVerification.value
    };

    const response: any = await request({
      url: '/api/auth/login',
      method: 'post',
      data: loginData,
    });

    const userData = response.data || response;
    
    if (userData.accessToken) {
      // 登录成功，清除失败次数
      localStorage.removeItem('loginAttempts');

      localStorage.setItem('token', userData.accessToken);
      localStorage.setItem('refreshToken', userData.refreshToken || '');
      
      const userInfo = {
        userId: userData.userId,
        username: userData.username,
        email: userData.email,
        firstName: userData.firstName,
        lastName: userData.lastName,
        tenantId: userData.tenantId,
        tenantCode: userData.tenantCode,
        tenantName: userData.tenantName,
        tenantRole: userData.tenantRole,
        displayName: userData.displayName,
        permissions: userData.permissions,
        accessibleTenantIds: userData.accessibleTenantIds,
        isActive: userData.active,
        isSuperAdmin: userData.superAdmin,
        platformRole: userData.platformRole
      };
      
      localStorage.setItem('user', JSON.stringify(userInfo));

      // 显示登录成功信息和权限范围
      if (userData.superAdmin) {
        ElMessage.success('🔧 系统总管理员登录成功！拥有平台最高权限');
      } else {
        const hierarchyInfo = userData.hierarchyDescription || '仅访问当前机构数据';
        ElMessage.success(`🏥 ${userData.tenantName} 登录成功！${hierarchyInfo}`);
      }

      router.push('/');
    } else {
      ElMessage.error(userData.message || '登录失败');
    }
  } catch (error: any) {
    
    // 记录登录失败次数
    const currentAttempts = parseInt(localStorage.getItem('loginAttempts') || '0');
    localStorage.setItem('loginAttempts', String(currentAttempts + 1));
    
    let errorMessage = '登录失败';
    if (error.response?.data?.message) {
      errorMessage = error.response.data.message;
    } else if (error.response?.data) {
      errorMessage = typeof error.response.data === 'string' 
        ? error.response.data 
        : JSON.stringify(error.response.data);
    } else if (error.message) {
      errorMessage = error.message;
    } else {
      errorMessage = '网络连接异常，请检查网络状态';
    }
    
    // 如果是验证码相关错误，重置验证码
    if (errorMessage.includes('验证码')) {
      resetCaptcha();
    }
    
    ElMessage.error(errorMessage);
  } finally {
    loading.value = false;
  }
};

// 验证码事件处理
function handleCaptchaVerified(data: { token: string; verification: string }) {
  console.log('✅ Admin验证码验证成功:', data);
  captchaVerified.value = true;
  captchaToken.value = data.token;
  captchaVerification.value = data.verification;
  ElMessage.success('验证码验证成功');
}


// 重置验证码状态（在登录失败时调用）
function resetCaptcha() {
  captchaVerified.value = false;
  captchaToken.value = '';
  captchaVerification.value = '';
  // 重置共享组件
  if (sharedCaptchaRef.value) {
    sharedCaptchaRef.value.reset();
  }
}

// 加载验证码
async function loadCaptcha() {
  captchaLoading.value = true;
  try {
    console.log('🔄 开始加载验证码...');
    const response = await getCaptcha();
    console.log('✅ 验证码API响应:', response);
    
    const data = (response as any)?.data?.data || (response as any)?.data || response;
    if (data && data.originalImageBase64) {
      Object.assign(captchaData, data);
      console.log('✅ 验证码数据加载成功:', {
        token: data.token,
        hasImage: !!data.originalImageBase64,
        hasJigsaw: !!data.jigsawImageBase64,
        y: data.y
      });
      resetSlider();
    } else {
      console.error('❌ 验证码数据格式错误:', data);
    }
  } catch (error) {
    console.error('❌ 加载验证码失败:', error);
  } finally {
    captchaLoading.value = false;
  }
}

// 重置滑块状态
function resetSlider() {
  sliderPosition.value = 2; // 从左边缘开始，留2px边距
  sliderVerified.value = false;
  isDragging.value = false;
  console.log('🔄 滑块已重置');
}

// 刷新验证码
async function refreshCaptcha() {
  console.log('🔄 刷新验证码...');
  await loadCaptcha();
}


// 简化的事件处理函数
function handleMouseDown(event: MouseEvent) {
  console.log('🖱️ 鼠标按下', event);
  startDrag(event);
}

function handleTouchStart(event: TouchEvent) {
  console.log('👆 触摸开始', event);
  event.preventDefault();
  startDrag(event);
}

function startDrag(event: MouseEvent | TouchEvent) {
  if (sliderVerified.value) return;
  
  console.log('🚀 开始拖拽', event.type);
  
  isDragging.value = true;
  
  const startCoords = getEventCoordinates(event);
  const startPos = sliderPosition.value;
  const containerRect = sliderContainer.value?.getBoundingClientRect();
  const maxWidth = containerRect ? containerRect.width - 32 : 268; // 300 - 32
  
  console.log('📍 初始状态:', { startCoords, startPos, maxWidth });
  
  function onMove(e: MouseEvent | TouchEvent) {
    if (!isDragging.value) return;
    
    const currentCoords = getEventCoordinates(e);
    const deltaX = currentCoords.x - startCoords.x;
    const newPos = Math.max(0, Math.min(maxWidth, startPos + deltaX));
    
    sliderPosition.value = newPos;
    
    // 实时计算对应的后端图片位置
    const sliderRatio = newPos / maxWidth;
    const backendImageX = Math.round(50 + sliderRatio * (310 - 47 - 50)); // 后端实际坐标
    console.log('📏 移动到:', { sliderPos: newPos, backendImageX, ratio: sliderRatio.toFixed(3) });
  }
  
  function onEnd() {
    console.log('🏁 拖拽结束');
    isDragging.value = false;
    
    // 移除事件监听器
    document.removeEventListener('mousemove', onMove);
    document.removeEventListener('mouseup', onEnd);
    document.removeEventListener('touchmove', onMove);
    document.removeEventListener('touchend', onEnd);
    
    // 验证滑动距离
    verifyCaptchaSlider();
  }
  
  // 添加事件监听器
  document.addEventListener('mousemove', onMove);
  document.addEventListener('mouseup', onEnd);
  document.addEventListener('touchmove', onMove, { passive: false });
  document.addEventListener('touchend', onEnd);
}

function getEventCoordinates(event: MouseEvent | TouchEvent) {
  if (event.type.startsWith('touch')) {
    const touchEvent = event as TouchEvent;
    return {
      x: touchEvent.touches[0]?.clientX || touchEvent.changedTouches[0]?.clientX || 0,
      y: touchEvent.touches[0]?.clientY || touchEvent.changedTouches[0]?.clientY || 0
    };
  } else {
    const mouseEvent = event as MouseEvent;
    return {
      x: mouseEvent.clientX,
      y: mouseEvent.clientY
    };
  }
}

// 计算拼图块在前端显示的位置
function calculateJigsawPosition() {
  // 将滑块位置按比例转换为前端图片显示位置
  // 前端图片容器宽度：300px，后端图片宽度：310px
  // 需要将后端的坐标系统映射到前端显示
  const frontendSliderTrackWidth = 300 - 36; // 264px (滑块可移动范围)
  const frontendImageWidth = 300; // 前端显示宽度
  const frontendPieceWidth = 47; // 修正：使用后端实际拼图宽度47px
  
  const sliderRatio = sliderPosition.value / frontendSliderTrackWidth;
  // 考虑到后端起始位置偏移，需要按比例映射到前端
  const frontendImageX = Math.round(sliderRatio * (frontendImageWidth - frontendPieceWidth));
  
  return frontendImageX;
}

async function verifyCaptchaSlider() {
  console.log('🔍 验证滑块位置:', sliderPosition.value);
  
  // 后端配置（来自SimpleCaptchaService.java）：
  // CAPTCHA_WIDTH = 310px，PIECE_WIDTH = 47px
  // 前端滑动条显示：300px，滑块宽度：36px
  
  const frontendSliderTrackWidth = 300 - 36; // 264px (前端滑块可移动距离)
  const backendImageWidth = 310; // 后端实际图片宽度
  const backendPieceWidth = 47; // 后端拼图块宽度
  const backendMovableWidth = backendImageWidth - backendPieceWidth - 50; // 减去起始位置留白50px
  
  // 计算比例：前端滑块位置 -> 后端图片坐标
  const sliderRatio = sliderPosition.value / frontendSliderTrackWidth;
  const imageX = Math.round(50 + sliderRatio * backendMovableWidth); // 50是后端起始位置
  
  console.log('📊 位置计算:', {
    frontendSliderPos: sliderPosition.value,
    frontendTrackWidth: frontendSliderTrackWidth,
    sliderRatio,
    backendImageX: imageX,
    backendMovableWidth,
    backendImageWidth,
    backendPieceWidth
  });
  
  try {
    const verifyData = {
      captchaType: 'blockPuzzle',
      token: captchaData.token,
      pointJson: JSON.stringify({ x: imageX, y: captchaData.y || 5 }),
      verification: captchaData.token
    };
    
    console.log('📤 发送验证数据:', verifyData);
    const result = await checkCaptcha(verifyData);
    const success = (result as any)?.data?.result || (result as any)?.result || false;
    
    if (success) {
      sliderVerified.value = true;
      captchaVerified.value = true;
      captchaToken.value = captchaData.token;
      captchaVerification.value = captchaData.token;
      
      console.log('✅ 验证成功!');
      ElMessage.success('验证码验证成功！');
      handleCaptchaVerified({
        token: captchaData.token,
        verification: captchaData.token
      });
    } else {
      console.log('❌ 验证失败，重置');
      ElMessage.error('验证失败，请重试');
      resetSlider();
      await loadCaptcha();
    }
  } catch (error) {
    console.error('❌ 验证出错:', error);
    ElMessage.error('验证出错，请重试');
    resetSlider();
    await loadCaptcha();
  }
}

</script>

<style scoped>
/* 自定义输入框样式 */
:deep(.custom-input .el-input__wrapper) {
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
}

:deep(.custom-input .el-input__wrapper:hover) {
  border-color: #5357A0;
  box-shadow: 0 0 0 1px rgba(83, 87, 160, 0.1);
}

:deep(.custom-input .el-input__wrapper.is-focus) {
  border-color: #5357A0;
  box-shadow: 0 0 0 2px rgba(83, 87, 160, 0.1);
}

/* 表单项间距 */
:deep(.el-form-item) {
  margin-bottom: 24px;
}

:deep(.el-form-item:last-child) {
  margin-bottom: 0;
}

/* 验证码样式 */
.captcha-section {
  margin-bottom: 24px;
}

.inline-captcha-container {
  margin: 15px 0;
}

/* 验证码图片容器 */
.captcha-image-container {
  position: relative;
  width: 300px;
  height: 150px;
  margin: 0 auto;
  border: 1px solid #e0e6ed;
  border-radius: 6px;
  overflow: hidden;
  background: #f8f9fa;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.captcha-image-container:hover {
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.15);
  transition: box-shadow 0.3s ease;
}

.captcha-bg-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.captcha-block-image {
  position: absolute;
  top: 0;
  width: 47px;  /* 修正：使用后端实际拼图块宽度 47px */
  height: 150px;
  object-fit: cover;
  z-index: 2;
}

/* 刷新按钮 */
.captcha-refresh-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 12px;
  z-index: 3;
  transition: all 0.2s ease;
}

.captcha-refresh-btn:hover {
  background: rgba(255, 255, 255, 1);
  transform: rotate(180deg);
}

/* 滑动条样式 - 重新设计 */
.captcha-slider {
  margin-top: 12px;
  width: 300px;
  margin-left: auto;
  margin-right: auto;
  position: relative;
  user-select: none;
  -webkit-user-select: none;
  -webkit-touch-callout: none;
}

.slider-track {
  position: relative;
  height: 40px;
  background: #f5f5f5;
  border: 2px solid #ddd;
  border-radius: 20px;
  overflow: hidden;
}

.slider-track-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.slider-text {
  color: #999;
  font-size: 14px;
  font-weight: 400;
  pointer-events: none;
  user-select: none;
}

.slider-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, #4CAF50 0%, #45a049 100%);
  border-radius: 18px;
  z-index: 2;
  transition: opacity 0.3s ease;
}

/* 滑块按钮 - 重新设计 */
.slider-button {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  border-radius: 50%;
  cursor: grab;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.4);
  z-index: 100;
  touch-action: none;
  user-select: none;
  -webkit-user-select: none;
  -webkit-touch-callout: none;
  transition: all 0.2s ease;
  border: 2px solid white;
}

.slider-button:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.5);
}

.slider-button:active,
.slider-button.slider-dragging {
  cursor: grabbing;
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(33, 150, 243, 0.6);
}

.slider-button.slider-verified {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.4);
}

.slider-icon {
  color: white;
  font-size: 16px;
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 响应式设计 */
@media (max-width: 480px) {
  .captcha-image-container,
  .captcha-slider {
    width: 280px;
  }
  
  .slider-text {
    font-size: 12px;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .grid-cols-1.md\\:grid-cols-2 {
    grid-template-columns: 1fr;
  }
}
</style>