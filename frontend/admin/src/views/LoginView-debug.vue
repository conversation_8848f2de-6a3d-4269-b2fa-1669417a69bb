<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
    <div class="max-w-md w-full bg-white rounded-2xl shadow-xl p-8">
      <div class="text-center mb-8">
        <h2 class="text-2xl font-bold text-gray-900">滑动验证码调试</h2>
        <p class="text-gray-600 mt-2">测试滑块功能</p>
      </div>

      <!-- 调试信息 -->
      <div class="mb-4 p-4 bg-gray-100 rounded text-sm">
        <div>isDragging: {{ isDragging }}</div>
        <div>sliderPosition: {{ sliderPosition }}</div>
        <div>sliderVerified: {{ sliderVerified }}</div>
        <div>captchaData.token: {{ captchaData.token ? '有' : '无' }}</div>
        <div>eventOptimizer loaded: {{ eventOptimizerLoaded }}</div>
      </div>

      <!-- 滑动验证码区域 -->
      <div class="captcha-section">
        <div v-if="captchaLoading" style="text-align: center; padding: 20px; color: #666;">
          🔄 正在加载验证码...
        </div>
        
        <div v-else-if="captchaData.originalImageBase64" class="captcha-display">
          <!-- 背景图片容器 -->
          <div class="captcha-image-container">
            <img 
              :src="'data:image/png;base64,' + captchaData.originalImageBase64"
              class="captcha-bg-image"
              draggable="false"
            />
            
            <!-- 滑块图片 -->
            <img 
              v-if="captchaData.jigsawImageBase64"
              :src="'data:image/png;base64,' + captchaData.jigsawImageBase64"
              class="captcha-block-image"
              :style="{ left: sliderPosition + 'px' }"
              draggable="false"
            />
            
            <!-- 刷新按钮 -->
            <div class="captcha-refresh-btn" @click="refreshCaptcha">
              <span>🔄</span>
            </div>
          </div>
          
          <!-- 滑动条 -->
          <div class="captcha-slider">
            <div class="slider-track">
              <div class="slider-track-bg">
                <span class="slider-text">{{ sliderVerified ? '' : '拖动滑块完成验证' }}</span>
              </div>
              
              <!-- 已滑动区域 -->
              <div class="slider-fill" :style="{ width: (sliderPosition + 20) + 'px' }">
                <span class="slider-text-success" v-if="sliderVerified">验证成功</span>
              </div>
            </div>
            
            <!-- 滑块按钮 -->
            <div 
              class="slider-button"
              :class="{ 'slider-verified': sliderVerified }"
              :style="{ left: sliderPosition + 'px' }"
              @mousedown="startSlideSimple"
              @touchstart="startSlideSimple"
            >
              <span class="slider-icon">{{ sliderVerified ? '✓' : '→' }}</span>
            </div>
          </div>
        </div>
        
        <div v-else style="text-align: center; padding: 20px; color: #999;">
          ❌ 验证码加载失败
          <button 
            type="button" 
            @click="loadCaptcha" 
            style="margin-left: 10px; padding: 5px 10px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; font-size: 12px;"
          >
            重新加载
          </button>
        </div>
      </div>

      <!-- 测试按钮 -->
      <div class="mt-4 space-x-2">
        <button @click="testEventOptimizer" class="px-4 py-2 bg-blue-500 text-white rounded">
          测试 EventOptimizer
        </button>
        <button @click="loadCaptcha" class="px-4 py-2 bg-green-500 text-white rounded">
          重新加载验证码
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { getCaptcha, checkCaptcha } from '@/api/captcha';

// 尝试导入 eventOptimizer
let addGlobalDragListeners: any = null;
let getEventCoordinates: any = null;
const eventOptimizerLoaded = ref(false);

try {
  const eventOptimizer = await import('@/utils/eventOptimizer.js');
  addGlobalDragListeners = eventOptimizer.addGlobalDragListeners;
  getEventCoordinates = eventOptimizer.getEventCoordinates;
  eventOptimizerLoaded.value = true;
  console.log('✅ EventOptimizer 加载成功');
} catch (error) {
  console.error('❌ EventOptimizer 加载失败:', error);
  eventOptimizerLoaded.value = false;
}

// 内联验证码状态
const captchaLoading = ref(false);
const captchaData = reactive({
  token: '',
  originalImageBase64: '',
  jigsawImageBase64: '',
  y: 0
});
const sliderPosition = ref(2);
const sliderVerified = ref(false);
const isDragging = ref(false);
const removeGlobalListeners = ref<(() => void) | null>(null);

onMounted(async () => {
  console.log('📱 Debug LoginView 已挂载，开始加载验证码...');
  await loadCaptcha();
});

// 加载验证码
async function loadCaptcha() {
  captchaLoading.value = true;
  try {
    console.log('🔄 开始获取验证码...');
    const response = await getCaptcha();
    console.log('📥 验证码响应:', response);
    
    if (response?.data?.data) {
      Object.assign(captchaData, response.data.data);
      console.log('✅ 验证码数据设置成功:', captchaData);
    } else if (response?.data) {
      Object.assign(captchaData, response.data);
      console.log('✅ 验证码数据设置成功 (备用格式):', captchaData);
    } else {
      throw new Error('验证码数据格式错误');
    }
  } catch (error) {
    console.error('❌ 获取验证码失败:', error);
    ElMessage.error('验证码加载失败');
  } finally {
    captchaLoading.value = false;
  }
}

// 刷新验证码
async function refreshCaptcha() {
  sliderPosition.value = 2;
  sliderVerified.value = false;
  isDragging.value = false;
  await loadCaptcha();
}

// 测试 EventOptimizer
function testEventOptimizer() {
  console.log('🧪 测试 EventOptimizer');
  console.log('addGlobalDragListeners:', typeof addGlobalDragListeners);
  console.log('getEventCoordinates:', typeof getEventCoordinates);
  
  if (getEventCoordinates) {
    const testEvent = { clientX: 100, clientY: 200 };
    const coords = getEventCoordinates(testEvent);
    console.log('测试坐标获取:', coords);
  }
}

// 简化的滑动开始函数
function startSlideSimple(event: MouseEvent | TouchEvent) {
  console.log('🖱️ 开始滑动 (简化版)', event.type);
  
  if (sliderVerified.value) {
    console.log('⚠️ 已验证，跳过');
    return;
  }
  
  isDragging.value = true;
  
  // 获取起始坐标
  let startX = 0;
  if (event.type === 'mousedown') {
    startX = (event as MouseEvent).clientX;
  } else if (event.type === 'touchstart') {
    const touchEvent = event as TouchEvent;
    startX = touchEvent.touches[0]?.clientX || 0;
  }
  
  const startPosition = sliderPosition.value;
  console.log('📍 起始位置:', { startX, startPosition });
  
  // 移动处理函数
  const handleMove = (e: MouseEvent | TouchEvent) => {
    if (!isDragging.value) return;
    
    let currentX = 0;
    if (e.type === 'mousemove') {
      currentX = (e as MouseEvent).clientX;
    } else if (e.type === 'touchmove') {
      const touchEvent = e as TouchEvent;
      currentX = touchEvent.touches[0]?.clientX || startX;
    }
    
    const diff = currentX - startX;
    const maxPosition = 300 - 32 - 2; // 容器宽度 - 滑块宽度 - 边距
    const newPosition = Math.max(2, Math.min(maxPosition, startPosition + diff));
    
    sliderPosition.value = newPosition;
    console.log('📏 移动中:', { currentX, diff, newPosition });
  };
  
  // 结束处理函数
  const handleEnd = async () => {
    if (!isDragging.value) return;
    
    console.log('🏁 滑动结束');
    isDragging.value = false;
    
    // 移除事件监听器
    document.removeEventListener('mousemove', handleMove);
    document.removeEventListener('mouseup', handleEnd);
    document.removeEventListener('touchmove', handleMove);
    document.removeEventListener('touchend', handleEnd);
    
    // 验证滑动距离
    const moveDistance = sliderPosition.value - 2;
    console.log('🎯 验证滑动距离:', moveDistance);
    
    if (moveDistance > 200) { // 简单的距离验证
      sliderVerified.value = true;
      console.log('✅ 验证成功 (简化版)');
      ElMessage.success('验证成功！');
    } else {
      console.log('❌ 验证失败，距离不够');
      ElMessage.error('请滑动到最右边');
      sliderPosition.value = 2; // 重置位置
    }
  };
  
  // 添加事件监听器
  document.addEventListener('mousemove', handleMove);
  document.addEventListener('mouseup', handleEnd);
  document.addEventListener('touchmove', handleMove);
  document.addEventListener('touchend', handleEnd);
  
  // 阻止默认行为
  event.preventDefault();
}
</script>

<style scoped>
/* 验证码样式 */
.captcha-section {
  margin-bottom: 24px;
}

/* 验证码图片容器 */
.captcha-image-container {
  position: relative;
  width: 300px;
  height: 150px;
  margin: 0 auto;
  border: 1px solid #e0e6ed;
  border-radius: 6px;
  overflow: hidden;
  background: #f8f9fa;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.captcha-bg-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.captcha-block-image {
  position: absolute;
  top: 0;
  width: 47px;  /* 修正：使用后端实际拼图块宽度 47px */
  height: 150px;
  object-fit: cover;
  z-index: 2;
}

/* 刷新按钮 */
.captcha-refresh-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 12px;
  z-index: 3;
  transition: all 0.3s ease;
}

.captcha-refresh-btn:hover {
  background: rgba(255, 255, 255, 1);
  transform: rotate(180deg);
}

/* 滑动条 */
.captcha-slider {
  width: 300px;
  height: 40px;
  margin: 10px auto 0;
  position: relative;
  background: #f0f2f5;
  border-radius: 20px;
  overflow: hidden;
}

.slider-track {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.slider-track-bg {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 14px;
}

.slider-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #45a049);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: width 0.3s ease;
}

.slider-text {
  user-select: none;
  pointer-events: none;
}

.slider-text-success {
  color: white;
  font-weight: bold;
  font-size: 14px;
}

.slider-button {
  position: absolute;
  top: 2px;
  width: 36px;
  height: 36px;
  background: white;
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 2;
  user-select: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.slider-button:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.slider-button.slider-verified {
  background: #4CAF50;
  color: white;
}

.slider-icon {
  font-size: 16px;
  font-weight: bold;
}
</style>