package com.assessment.service;

import com.assessment.dto.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.security.SecureRandom;
import java.time.Duration;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 简单滑动验证码服务
 * 基于Java AWT实现的滑动拼图验证码
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-23
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SimpleCaptchaService {
    
    static {
        // 设置Java AWT headless模式，允许在没有图形界面的环境中生成图片
        System.setProperty("java.awt.headless", "true");
    }

    private final RedisTemplate<String, Object> redisTemplate;
    
    private static final String CAPTCHA_PREFIX = "simple_captcha:";
    private static final int CAPTCHA_WIDTH = 310;
    private static final int CAPTCHA_HEIGHT = 155;
    private static final int PIECE_WIDTH = 47;
    private static final int PIECE_HEIGHT = 47;
    private static final int TOLERANCE = 5; // 允许的误差像素
    
    private final SecureRandom random = new SecureRandom();
    
    /**
     * 生成滑动验证码
     */
    public ApiResponse<Map<String, Object>> generateCaptcha() {
        try {
            log.info("开始生成验证码");
            String token = UUID.randomUUID().toString();
            
            // 生成随机滑块位置
            int x = random.nextInt(CAPTCHA_WIDTH - PIECE_WIDTH - 50) + 50; // 留出起始和结束位置
            int y = random.nextInt(CAPTCHA_HEIGHT - PIECE_HEIGHT - 20) + 10; // 留出上下边距
            
            // 创建背景图
            BufferedImage backgroundImage = createBackgroundImage();
            
            // 创建滑块图片和背景缺口
            BufferedImage pieceImage = createPieceImage(backgroundImage, x, y);
            
            // 将图片转为Base64
            String backgroundBase64 = imageToBase64(backgroundImage);
            String pieceBase64 = imageToBase64(pieceImage);
            
            // 生成验证密钥
            String secretKey = generateSecretKey();
            
            // 存储验证信息到Redis
            Map<String, Object> captchaInfo = new HashMap<>();
            captchaInfo.put("x", x);
            captchaInfo.put("y", y);
            captchaInfo.put("secretKey", secretKey);
            captchaInfo.put("timestamp", System.currentTimeMillis());
            
            redisTemplate.opsForValue().set(
                CAPTCHA_PREFIX + token, 
                captchaInfo, 
                Duration.ofMinutes(5)
            );
            
            // 返回验证码数据
            Map<String, Object> result = new HashMap<>();
            result.put("token", token);
            result.put("originalImageBase64", backgroundBase64);
            result.put("jigsawImageBase64", pieceBase64);
            result.put("secretKey", secretKey);
            result.put("result", false);
            result.put("y", y); // 添加Y坐标到响应中
            
            log.info("验证码生成成功，token: {}, 滑块位置: ({}, {})", token, x, y);
            return ApiResponse.success(result);
            
        } catch (Exception e) {
            log.error("生成验证码失败: {}", e.getMessage(), e);
            return ApiResponse.error("验证码生成失败: " + e.getMessage());
        }
    }
    
    /**
     * 校验滑动验证码
     */
    public ApiResponse<Map<String, Object>> checkCaptcha(String token, String pointJson, String verification) {
        try {
            if (token == null || pointJson == null || verification == null) {
                return ApiResponse.error("参数不完整");
            }
            
            // 从Redis获取验证信息
            Object captchaObj = redisTemplate.opsForValue().get(CAPTCHA_PREFIX + token);
            if (captchaObj == null) {
                return ApiResponse.error("验证码已过期或无效");
            }
            
            @SuppressWarnings("unchecked")
            Map<String, Object> captchaInfo = (Map<String, Object>) captchaObj;
            
            // 验证密钥
            String storedSecretKey = (String) captchaInfo.get("secretKey");
            if (!verification.equals(storedSecretKey)) {
                return ApiResponse.error("验证密钥错误");
            }
            
            // 解析滑动位置
            String cleanPointJson = pointJson.replace("{", "").replace("}", "").replace("\"", "");
            String[] pairs = cleanPointJson.split(",");
            int userX = 0;
            
            for (String pair : pairs) {
                String[] kv = pair.split(":");
                if (kv.length == 2 && "x".equals(kv[0].trim())) {
                    userX = Integer.parseInt(kv[1].trim());
                    break;
                }
            }
            
            // 获取正确位置
            int correctX = (Integer) captchaInfo.get("x");
            
            // 验证位置是否正确（允许一定误差）
            boolean isValid = Math.abs(userX - correctX) <= TOLERANCE;
            
            Map<String, Object> result = new HashMap<>();
            result.put("result", isValid);
            result.put("message", isValid ? "验证成功" : "验证失败");
            
            if (isValid) {
                // 验证成功，删除Redis中的数据
                redisTemplate.delete(CAPTCHA_PREFIX + token);
                log.info("验证码校验成功，token: {}, 用户位置: {}, 正确位置: {}", token, userX, correctX);
            } else {
                log.warn("验证码校验失败，token: {}, 用户位置: {}, 正确位置: {}", token, userX, correctX);
            }
            
            return ApiResponse.success(result);
            
        } catch (Exception e) {
            log.error("校验验证码失败", e);
            return ApiResponse.error("验证码校验失败");
        }
    }
    
    /**
     * 创建背景图片 - 使用AjCaptcha专业背景图片
     */
    private BufferedImage createBackgroundImage() {
        try {
            // 随机选择一张AjCaptcha背景图片
            String[] backgroundFiles = {"bg_chinese.png"};
            String selectedBg = backgroundFiles[random.nextInt(backgroundFiles.length)];
            
            // 从classpath加载背景图片
            try (var inputStream = getClass().getClassLoader().getResourceAsStream("images/" + selectedBg)) {
                if (inputStream != null) {
                    BufferedImage originalBg = ImageIO.read(inputStream);
                    
                    // 调整到目标尺寸
                    BufferedImage resizedBg = new BufferedImage(CAPTCHA_WIDTH, CAPTCHA_HEIGHT, BufferedImage.TYPE_INT_RGB);
                    Graphics2D g = resizedBg.createGraphics();
                    g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
                    g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                    g.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
                    
                    // 缩放并居中绘制背景图片
                    g.drawImage(originalBg, 0, 0, CAPTCHA_WIDTH, CAPTCHA_HEIGHT, null);
                    
                    // 添加轻微的图片优化效果
                    addImageEnhancement(resizedBg);
                    
                    g.dispose();
                    log.info("✅ 成功加载AjCaptcha背景图片: {}", selectedBg);
                    return resizedBg;
                }
            }
        } catch (Exception e) {
            log.warn("⚠️ 加载AjCaptcha背景图片失败，使用默认背景: {}", e.getMessage());
        }
        
        // 如果加载失败，使用默认的彩色背景
        return createDefaultBackgroundImage();
    }
    
    /**
     * 创建默认背景图片（备用方案）
     */
    private BufferedImage createDefaultBackgroundImage() {
        BufferedImage image = new BufferedImage(CAPTCHA_WIDTH, CAPTCHA_HEIGHT, BufferedImage.TYPE_INT_RGB);
        Graphics2D g = image.createGraphics();
        
        // 高质量渲染设置
        g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        
        // 创建专业的渐变背景
        GradientPaint gradient = new GradientPaint(
            0, 0, new Color(240, 248, 255), // 淡蓝色
            CAPTCHA_WIDTH, CAPTCHA_HEIGHT, new Color(230, 230, 250) // 淡紫色
        );
        g.setPaint(gradient);
        g.fillRect(0, 0, CAPTCHA_WIDTH, CAPTCHA_HEIGHT);
        
        // 添加轻微的纹理
        addDefaultTexturePattern(g);
        
        g.dispose();
        return image;
    }
    
    /**
     * 添加图片增强效果
     */
    private void addImageEnhancement(BufferedImage image) {
        Graphics2D g = image.createGraphics();
        g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        // 添加轻微的亮度调整
        g.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, 0.95f));
        
        // 添加时间戳确保图片唯一性
        g.setColor(new Color(255, 255, 255, 60));
        g.setFont(new Font("Arial", Font.PLAIN, 8));
        String timestamp = String.valueOf(System.currentTimeMillis() % 10000);
        g.drawString("AJ:" + timestamp, CAPTCHA_WIDTH - 60, CAPTCHA_HEIGHT - 8);
        
        g.dispose();
    }
    
    /**
     * 添加默认纹理图案
     */
    private void addDefaultTexturePattern(Graphics2D g) {
        g.setColor(new Color(255, 255, 255, 30));
        g.setStroke(new BasicStroke(0.5f));
        
        // 添加对角线纹理
        for (int i = 0; i < CAPTCHA_WIDTH + CAPTCHA_HEIGHT; i += 20) {
            g.drawLine(i, 0, i - CAPTCHA_HEIGHT, CAPTCHA_HEIGHT);
        }
    }
    
    /**
     * 创建滑块图片并在背景图上挖洞
     */
    private BufferedImage createPieceImage(BufferedImage backgroundImage, int x, int y) {
        // 创建滑块图片，高度与背景图相同
        BufferedImage pieceImage = new BufferedImage(PIECE_WIDTH, CAPTCHA_HEIGHT, BufferedImage.TYPE_INT_ARGB);
        Graphics2D pieceG = pieceImage.createGraphics();
        pieceG.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        // 设置透明背景
        pieceG.setComposite(AlphaComposite.Clear);
        pieceG.fillRect(0, 0, PIECE_WIDTH, CAPTCHA_HEIGHT);
        
        // 恢复正常绘制模式
        pieceG.setComposite(AlphaComposite.SrcOver);
        
        // 重要：生成固定的拼图形状，确保拼图块和缺口形状一致
        // 先生成随机的形状模式，然后在拼图块和缺口中复用
        int[] tabPattern = {
            random.nextInt(3), // topTab
            random.nextInt(3), // rightTab  
            random.nextInt(3), // bottomTab
            random.nextInt(3)  // leftTab
        };
        
        // 创建拼图形状路径 - 在拼图块图片中坐标从(0,0)开始
        java.awt.Shape puzzleShape = createPuzzleShapePath(0, 0, PIECE_WIDTH, PIECE_HEIGHT, tabPattern);
        
        // 从背景图上提取拼图区域的图像
        BufferedImage puzzleArea = backgroundImage.getSubimage(x, y, PIECE_WIDTH, PIECE_HEIGHT);
        
        // 应用形状遮罩，创建真实的拼图块
        pieceG.setClip(puzzleShape);
        pieceG.drawImage(puzzleArea, 0, 0, null);
        
        // 添加精致的拼图块边框效果
        pieceG.setClip(null); // 移除裁剪遮罩
        
        // 外边框 - 深色阴影
        pieceG.setStroke(new BasicStroke(2.0f, BasicStroke.CAP_ROUND, BasicStroke.JOIN_ROUND));
        pieceG.setColor(new Color(0, 0, 0, 80)); // 深色阴影
        pieceG.draw(puzzleShape);
        
        // 内边框 - 白色高光（使用相同的形状模式）
        pieceG.setStroke(new BasicStroke(1.0f, BasicStroke.CAP_ROUND, BasicStroke.JOIN_ROUND));
        pieceG.setColor(new Color(255, 255, 255, 160)); // 白色高光
        java.awt.Shape innerBorder = createPuzzleShapePath(1, 1, PIECE_WIDTH - 2, PIECE_HEIGHT - 2, tabPattern);
        pieceG.draw(innerBorder);
        
        // 添加轻微的立体感阴影（使用相同的形状模式）
        pieceG.setStroke(new BasicStroke(0.5f));
        pieceG.setColor(new Color(0, 0, 0, 40));
        java.awt.Shape shadowShape = createPuzzleShapePath(1, 2, PIECE_WIDTH - 2, PIECE_HEIGHT - 2, tabPattern);
        pieceG.draw(shadowShape);
        
        pieceG.dispose();
        
        // 在背景图上创建专业的缺口效果
        Graphics2D bgG = backgroundImage.createGraphics();
        bgG.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        // 使用相同的形状模式创建背景缺口
        java.awt.Shape bgPuzzleShape = createPuzzleShapePath(x, y, PIECE_WIDTH, PIECE_HEIGHT, tabPattern);
        
        // 第一步：完全透明化拼图区域
        Graphics2D clearG = (Graphics2D) bgG.create();
        clearG.setComposite(AlphaComposite.Clear);
        clearG.fill(bgPuzzleShape);
        clearG.dispose();
        
        // 第二步：添加精致的缺口边框
        // 外边框 - 深色轮廓
        bgG.setStroke(new BasicStroke(1.5f, BasicStroke.CAP_ROUND, BasicStroke.JOIN_ROUND));
        bgG.setColor(new Color(0, 0, 0, 120)); // 深色边框
        bgG.draw(bgPuzzleShape);
        
        // 内边框 - 高光效果（使用相同的形状模式）
        bgG.setStroke(new BasicStroke(0.8f, BasicStroke.CAP_ROUND, BasicStroke.JOIN_ROUND));
        bgG.setColor(new Color(255, 255, 255, 100)); // 白色高光
        java.awt.Shape innerHighlight = createPuzzleShapePath(x + 1, y + 1, PIECE_WIDTH - 2, PIECE_HEIGHT - 2, tabPattern);
        bgG.draw(innerHighlight);
        
        // 第三步：添加轻微的阴影效果（使用相同的形状模式）
        bgG.setStroke(new BasicStroke(0.5f));
        bgG.setColor(new Color(0, 0, 0, 60)); // 轻微阴影
        java.awt.Shape shadowOutline = createPuzzleShapePath(x + 1, y + 2, PIECE_WIDTH - 1, PIECE_HEIGHT - 1, tabPattern);
        bgG.draw(shadowOutline);
        
        // 应用轻微的边缘模糊，让切割更自然
        applyGaussianBlur(backgroundImage, x, y, PIECE_WIDTH, PIECE_HEIGHT);
        
        bgG.dispose();
        
        return pieceImage;
    }
    
    /**
     * 创建专业拼图形状路径 - 参考AjCaptcha设计
     */
    private java.awt.Shape createPuzzleShapePath(int x, int y, int width, int height) {
        return createPuzzleShapePath(x, y, width, height, null);
    }
    
    /**
     * 创建专业拼图形状路径 - 支持固定形状参数
     */
    private java.awt.Shape createPuzzleShapePath(int x, int y, int width, int height, int[] tabPattern) {
        java.awt.geom.Path2D.Float path = new java.awt.geom.Path2D.Float();
        
        // 基础尺寸参数
        int radius = width / 8; // 圆角和凸起的半径
        int tabWidth = width / 4; // 凸起宽度
        int tabHeight = height / 6; // 凸起高度
        
        // 决定四个方向是否有凸起（0=凹陷，1=凸起，2=平直）
        int topTab, rightTab, bottomTab, leftTab;
        if (tabPattern != null && tabPattern.length >= 4) {
            // 使用预定义的形状模式
            topTab = tabPattern[0];
            rightTab = tabPattern[1];
            bottomTab = tabPattern[2];
            leftTab = tabPattern[3];
        } else {
            // 随机生成形状（向后兼容）
            topTab = random.nextInt(3);
            rightTab = random.nextInt(3);
            bottomTab = random.nextInt(3);
            leftTab = random.nextInt(3);
        }
        
        // 起始点：左上角
        path.moveTo(x + radius, y);
        
        // 顶边 - 可能有凸起或凹陷
        if (topTab == 1) { // 凸起
            path.lineTo(x + width/2 - tabWidth/2, y);
            path.curveTo(x + width/2 - tabWidth/2, y - tabHeight, 
                        x + width/2 + tabWidth/2, y - tabHeight, 
                        x + width/2 + tabWidth/2, y);
        } else if (topTab == 0) { // 凹陷
            path.lineTo(x + width/2 - tabWidth/2, y);
            path.curveTo(x + width/2 - tabWidth/2, y + tabHeight, 
                        x + width/2 + tabWidth/2, y + tabHeight, 
                        x + width/2 + tabWidth/2, y);
        }
        
        // 到右上角
        path.lineTo(x + width - radius, y);
        path.curveTo(x + width, y, x + width, y, x + width, y + radius);
        
        // 右边 - 可能有凸起或凹陷
        if (rightTab == 1) { // 凸起
            path.lineTo(x + width, y + height/2 - tabWidth/2);
            path.curveTo(x + width + tabHeight, y + height/2 - tabWidth/2, 
                        x + width + tabHeight, y + height/2 + tabWidth/2, 
                        x + width, y + height/2 + tabWidth/2);
        } else if (rightTab == 0) { // 凹陷
            path.lineTo(x + width, y + height/2 - tabWidth/2);
            path.curveTo(x + width - tabHeight, y + height/2 - tabWidth/2, 
                        x + width - tabHeight, y + height/2 + tabWidth/2, 
                        x + width, y + height/2 + tabWidth/2);
        }
        
        // 到右下角
        path.lineTo(x + width, y + height - radius);
        path.curveTo(x + width, y + height, x + width, y + height, x + width - radius, y + height);
        
        // 底边 - 可能有凸起或凹陷
        if (bottomTab == 1) { // 凸起
            path.lineTo(x + width/2 + tabWidth/2, y + height);
            path.curveTo(x + width/2 + tabWidth/2, y + height + tabHeight, 
                        x + width/2 - tabWidth/2, y + height + tabHeight, 
                        x + width/2 - tabWidth/2, y + height);
        } else if (bottomTab == 0) { // 凹陷
            path.lineTo(x + width/2 + tabWidth/2, y + height);
            path.curveTo(x + width/2 + tabWidth/2, y + height - tabHeight, 
                        x + width/2 - tabWidth/2, y + height - tabHeight, 
                        x + width/2 - tabWidth/2, y + height);
        }
        
        // 到左下角
        path.lineTo(x + radius, y + height);
        path.curveTo(x, y + height, x, y + height, x, y + height - radius);
        
        // 左边 - 可能有凸起或凹陷
        if (leftTab == 1) { // 凸起
            path.lineTo(x, y + height/2 + tabWidth/2);
            path.curveTo(x - tabHeight, y + height/2 + tabWidth/2, 
                        x - tabHeight, y + height/2 - tabWidth/2, 
                        x, y + height/2 - tabWidth/2);
        } else if (leftTab == 0) { // 凹陷
            path.lineTo(x, y + height/2 + tabWidth/2);
            path.curveTo(x + tabHeight, y + height/2 + tabWidth/2, 
                        x + tabHeight, y + height/2 - tabWidth/2, 
                        x, y + height/2 - tabWidth/2);
        }
        
        // 回到起始点
        path.lineTo(x, y + radius);
        path.curveTo(x, y, x, y, x + radius, y);
        
        path.closePath();
        return path;
    }
    
    
    /**
     * 应用高斯模糊到指定区域 - 参考AjCaptcha边缘处理
     */
    private void applyGaussianBlur(BufferedImage image, int x, int y, int width, int height) {
        try {
            // 创建模糊内核
            float[] blurKernel = {
                0.045f, 0.122f, 0.045f,
                0.122f, 0.332f, 0.122f,
                0.045f, 0.122f, 0.045f
            };
            
            java.awt.image.ConvolveOp blurOp = new java.awt.image.ConvolveOp(
                new java.awt.image.Kernel(3, 3, blurKernel), 
                java.awt.image.ConvolveOp.EDGE_NO_OP, 
                null
            );
            
            // 提取边缘区域进行模糊处理
            int blurMargin = 5;
            int blurX = Math.max(0, x - blurMargin);
            int blurY = Math.max(0, y - blurMargin);
            int blurWidth = Math.min(image.getWidth() - blurX, width + 2 * blurMargin);
            int blurHeight = Math.min(image.getHeight() - blurY, height + 2 * blurMargin);
            
            if (blurWidth > 0 && blurHeight > 0) {
                BufferedImage regionToBlur = image.getSubimage(blurX, blurY, blurWidth, blurHeight);
                BufferedImage blurredRegion = blurOp.filter(regionToBlur, null);
                
                Graphics2D g = image.createGraphics();
                g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                g.drawImage(blurredRegion, blurX, blurY, null);
                g.dispose();
            }
        } catch (Exception e) {
            log.warn("应用高斯模糊失败: {}", e.getMessage());
        }
    }
    
    /**
     * 增强随机干扰点生成 - 参考AjCaptcha防机器学习
     */
    private void addRandomInterference(BufferedImage image) {
        Graphics2D g = image.createGraphics();
        g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        // 添加随机噪点
        for (int i = 0; i < 20; i++) {
            int x = random.nextInt(image.getWidth());
            int y = random.nextInt(image.getHeight());
            g.setColor(new Color(
                random.nextInt(256), 
                random.nextInt(256), 
                random.nextInt(256), 
                50 + random.nextInt(100)
            ));
            g.fillOval(x, y, 2 + random.nextInt(3), 2 + random.nextInt(3));
        }
        
        // 添加随机线条
        for (int i = 0; i < 5; i++) {
            g.setColor(new Color(255, 255, 255, 30 + random.nextInt(50)));
            g.setStroke(new BasicStroke(1.0f + random.nextFloat()));
            g.drawLine(
                random.nextInt(image.getWidth()), 
                random.nextInt(image.getHeight()),
                random.nextInt(image.getWidth()), 
                random.nextInt(image.getHeight())
            );
        }
        
        g.dispose();
    }
    
    /**
     * 图片转Base64
     */
    private String imageToBase64(BufferedImage image) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(image, "PNG", baos);
        byte[] imageBytes = baos.toByteArray();
        return Base64.getEncoder().encodeToString(imageBytes);
    }
    
    /**
     * 生成验证密钥
     */
    private String generateSecretKey() {
        return UUID.randomUUID().toString().replace("-", "");
    }
}