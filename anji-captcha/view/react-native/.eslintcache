[{"C:\\Users\\<USER>\\Desktop\\my-app\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\my-app\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\my-app\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Desktop\\my-app\\src\\pages\\index.js": "4", "C:\\Users\\<USER>\\Desktop\\my-app\\src\\Pages\\index.js": "5", "C:\\Users\\<USER>\\Desktop\\my-app\\src\\api\\ase.js": "6", "C:\\Users\\<USER>\\Desktop\\my-app\\src\\components\\verifyPoint.js": "7", "C:\\Users\\<USER>\\Desktop\\my-app\\src\\api\\base.js": "8", "C:\\Users\\<USER>\\Desktop\\my-app\\src\\api\\axios.js": "9", "C:\\Users\\<USER>\\Desktop\\my-app\\src\\components\\verifyPointFixed.js": "10", "C:\\Users\\<USER>\\Desktop\\my-app\\src\\components\\verifySlide.js": "11", "C:\\Users\\<USER>\\Desktop\\my-app\\src\\components\\verifySlideFixed.js": "12", "D:\\代码库\\行为验证码\\captcha\\view\\react-native\\src\\index.js": "13", "D:\\代码库\\行为验证码\\captcha\\view\\react-native\\src\\App.js": "14", "D:\\代码库\\行为验证码\\captcha\\view\\react-native\\src\\reportWebVitals.js": "15", "D:\\代码库\\行为验证码\\captcha\\view\\react-native\\src\\Pages\\index.js": "16", "D:\\代码库\\行为验证码\\captcha\\view\\react-native\\src\\components\\verifySlide.js": "17", "D:\\代码库\\行为验证码\\captcha\\view\\react-native\\src\\components\\verifyPointFixed.js": "18", "D:\\代码库\\行为验证码\\captcha\\view\\react-native\\src\\components\\verifySlideFixed.js": "19", "D:\\代码库\\行为验证码\\captcha\\view\\react-native\\src\\components\\verifyPoint.js": "20", "D:\\代码库\\行为验证码\\captcha\\view\\react-native\\src\\api\\base.js": "21", "D:\\代码库\\行为验证码\\captcha\\view\\react-native\\src\\api\\ase.js": "22", "D:\\代码库\\行为验证码\\captcha\\view\\react-native\\src\\api\\axios.js": "23"}, {"size": 500, "mtime": 499162500000, "results": "24", "hashOfConfig": "25"}, {"size": 207, "mtime": 1609241353427, "results": "26", "hashOfConfig": "25"}, {"size": 362, "mtime": 499162500000, "results": "27", "hashOfConfig": "25"}, {"size": 319, "mtime": 1609231658518, "results": "28", "hashOfConfig": "25"}, {"size": 2408, "mtime": 1609396599059, "results": "29", "hashOfConfig": "25"}, {"size": 426, "mtime": 1609241261869, "results": "30", "hashOfConfig": "25"}, {"size": 6270, "mtime": 1609335182256, "results": "31", "hashOfConfig": "25"}, {"size": 292, "mtime": 1609314678308, "results": "32", "hashOfConfig": "25"}, {"size": 676, "mtime": 1609306290959, "results": "33", "hashOfConfig": "25"}, {"size": 7182, "mtime": 1609396581331, "results": "34", "hashOfConfig": "25"}, {"size": 10615, "mtime": 1609395853784, "results": "35", "hashOfConfig": "25"}, {"size": 11421, "mtime": 1609396580322, "results": "36", "hashOfConfig": "25"}, {"size": 470, "mtime": 1610434085676, "results": "37", "hashOfConfig": "38"}, {"size": 222, "mtime": 1610434085664, "results": "39", "hashOfConfig": "38"}, {"size": 375, "mtime": 1610434085676, "results": "40", "hashOfConfig": "38"}, {"size": 2408, "mtime": 1610434085665, "results": "41", "hashOfConfig": "38"}, {"size": 12189, "mtime": 1611817771485, "results": "42", "hashOfConfig": "38"}, {"size": 8529, "mtime": 1611818126325, "results": "43", "hashOfConfig": "38"}, {"size": 13022, "mtime": 1611817967678, "results": "44", "hashOfConfig": "38"}, {"size": 7774, "mtime": 1611818066634, "results": "45", "hashOfConfig": "38"}, {"size": 292, "mtime": 1610434085668, "results": "46", "hashOfConfig": "38"}, {"size": 426, "mtime": 1610434085666, "results": "47", "hashOfConfig": "38"}, {"size": 734, "mtime": 1611818146864, "results": "48", "hashOfConfig": "38"}, {"filePath": "49", "messages": "50", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "51"}, "k8bipt", {"filePath": "52", "messages": "53", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "51"}, {"filePath": "54", "messages": "55", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "51"}, {"filePath": "56", "messages": "57", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "51"}, {"filePath": "62", "messages": "63", "errorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "64", "usedDeprecatedRules": "51"}, {"filePath": "65", "messages": "66", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "51"}, {"filePath": "67", "messages": "68", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "51"}, {"filePath": "69", "messages": "70", "errorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "71", "messages": "72", "errorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "73", "usedDeprecatedRules": "51"}, {"filePath": "74", "messages": "75", "errorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1hpz2kw", {"filePath": "78", "messages": "79", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "errorCount": 0, "warningCount": 25, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "86", "messages": "87", "errorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "88", "usedDeprecatedRules": "89"}, {"filePath": "90", "messages": "91", "errorCount": 0, "warningCount": 24, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "92", "messages": "93", "errorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "94", "usedDeprecatedRules": "89"}, {"filePath": "95", "messages": "96", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\my-app\\src\\index.js", [], ["101", "102"], "C:\\Users\\<USER>\\Desktop\\my-app\\src\\App.js", [], "C:\\Users\\<USER>\\Desktop\\my-app\\src\\reportWebVitals.js", [], "C:\\Users\\<USER>\\Desktop\\my-app\\src\\pages\\index.js", [], "C:\\Users\\<USER>\\Desktop\\my-app\\src\\Pages\\index.js", [], "C:\\Users\\<USER>\\Desktop\\my-app\\src\\api\\ase.js", [], "C:\\Users\\<USER>\\Desktop\\my-app\\src\\components\\verifyPoint.js", ["103", "104", "105", "106", "107", "108", "109", "110", "111", "112"], "import React, { Component } from 'react'\r\nimport { getPicture, reqCheck} from '../api/base.js'\r\nimport '../assets/index.css';\r\nimport {aesEncrypt} from \"../api/ase.js\";\r\n\r\nclass VerifyPoints extends Component {\r\n  constructor(props) {\r\n    super(props);\r\n    this.state = {\r\n      secretKey: '', //后端返回的ase加密秘钥\r\n      checkNum: 3, //默认需要点击的字数\r\n      fontPos: [], //选中的坐标信息\r\n      checkPosArr: [], //用户点击的坐标\r\n      num: 1, //点击的记数\r\n      pointBackImgBase: '', //后端获取到的背景图片\r\n      poinTextList: [], //后端返回的点击字体顺序\r\n      backToken: '', //后端返回的token值\r\n      captchaType: 'clickWord',\r\n      setSize: {\r\n        imgHeight: 0,\r\n        imgWidth: 0,\r\n        barHeight: 0,\r\n        barWidth: 0,\r\n      },\r\n      tempPoints: [],\r\n      text: '',\r\n      barAreaColor: 'rgb(0,0,0)',\r\n      barAreaBorderColor: 'rgb(221, 221, 221)',\r\n      showRefresh: true,\r\n      bindingClick: true,\r\n    };\r\n  }\r\n  componentDidMount() {\r\n    this.getData()\r\n  }\r\n  // 刷新\r\n  refresh = () => {\r\n    this.getData()\r\n    this.setState({\r\n      num: 1,\r\n      tempPoints: [],\r\n      bindingClick: true,\r\n      barAreaColor: 'rgb(0,0,0)',\r\n      barAreaBorderColor: 'rgb(221, 221, 221)',\r\n    })\r\n  }\r\n  // 初始化数据\r\n  getData() {\r\n    getPicture({captchaType: this.state.captchaType}).then(res => {\r\n      if(res.repCode == '0000') {\r\n        this.setState({\r\n          pointBackImgBase: res.repData.originalImageBase64,\r\n          backToken: res.repData.token,\r\n          secretKey: res.repData.secretKey,\r\n          text: '请依次点击【' + res.repData.wordList + '】'\r\n        })\r\n      }\r\n    })\r\n  }\r\n  \r\n  canvasClick = (e) => {\r\n    if(this.state.bindingClick) {\r\n      this.state.tempPoints.push(this.getMousePos(e))\r\n      this.setState({\r\n        tempPoints: this.state.tempPoints\r\n      })\r\n      if(this.state.num == this.state.checkNum) {\r\n        this.setState({\r\n          bindingClick: false\r\n        })\r\n        let data = {\r\n          captchaType:this.state.captchaType,\r\n          \"pointJson\":this.state.secretKey? aesEncrypt(JSON.stringify(this.state.tempPoints),this.state.secretKey):JSON.stringify(this.state.tempPoints),\r\n          \"token\":this.state.backToken\r\n        }\r\n        reqCheck(data).then(res => {\r\n          if(res.repCode == '0000') {\r\n            this.setState({\r\n              text: '验证成功',\r\n              barAreaColor: '#4cae4c',\r\n              barAreaBorderColor: '#5cb85c'\r\n            })\r\n            setTimeout(() => {\r\n              this.refresh()\r\n            }, 1500)\r\n          } else {\r\n            this.setState({\r\n              text: res.repMsg,\r\n              barAreaColor: '#d9534f',\r\n              barAreaBorderColor: '#d9534f'\r\n            })\r\n            setTimeout(() => {\r\n                this.refresh();\r\n            }, 1000);\r\n          }\r\n        })\r\n      }\r\n      if(this.state.num < this.state.checkNum) {\r\n        this.createPoint(this.getMousePos(e))\r\n        this.setState({\r\n          num: this.state.num++\r\n        })\r\n      } \r\n    }\r\n  }\r\n   //获取坐标\r\n  getMousePos =(e) => {\r\n    var x = e.nativeEvent.offsetX\r\n    var y = e.nativeEvent.offsetY\r\n    return {x, y}\r\n  }\r\n  // 创建坐标点\r\n  createPoint = () => {\r\n    let num = this.state.num++\r\n    this.setState({\r\n      num\r\n    })\r\n  }\r\n\r\n  //坐标转换函数\r\n  pointTransfrom = (pointArr,imgSize) => {\r\n    var newPointArr = pointArr.map(p=>{\r\n        let x = Math.round(310 * p.x/parseInt(imgSize.imgWidth)) \r\n        let y =Math.round(155 * p.y/parseInt(imgSize.imgHeight)) \r\n        return {x,y}\r\n    })\r\n    // console.log(newPointArr,\"newPointArr\");\r\n    return newPointArr\r\n  }\r\n\r\n  render() {\r\n    let tempPoints = this.state.tempPoints\r\n    const { mode, captchaType, vSpace, imgSize, barSize, setSize, pointBackImgBase } = this.props;\r\n    return (\r\n      <div style={{ position: 'relative' }}>\r\n        <div className='verify-img-out'>\r\n          <div\r\n            className='verify-img-panel'\r\n            style={{\r\n              width: setSize.imgWidth + 'px',\r\n              height: setSize.imgHeight + 'px',\r\n              backgroundSize: setSize.imgWidth + 'px' + ' ' + setSize.imgHeight + 'px',\r\n              marginBottom: vSpace + 'px',\r\n            }}\r\n          >\r\n            <div className='verify-refresh' style={{ zIndex: 3 }} onClick={this.refresh}>\r\n              <i className='iconfont icon-refresh'></i>\r\n            </div>\r\n            <img src={'data:image/png;base64,' + this.state.pointBackImgBase} alt=\"\" style={{width:'100%',height:'100%',display:'block'}} onClick={($event) => this.canvasClick($event)}/>\r\n\r\n            {tempPoints.map((tempPoint, index) => {\r\n              return (\r\n                <div\r\n                  key={index}\r\n                  className=\"point-area\"\r\n                  style={{\r\n                    backgroundColor: '#1abd6c',\r\n                    color: '#fff',\r\n                    zIndex: 9999,\r\n                    width: '20px',\r\n                    height: '20px',\r\n                    textAlign: 'center',\r\n                    lineHeight: '20px',\r\n                    borderRadius: '50%',\r\n                    position: 'absolute',\r\n                    top: parseInt(tempPoint.y - 10) + 'px',\r\n                    left: parseInt(tempPoint.x - 10) + 'px',\r\n                    overflow:'hidden'\r\n                  }}\r\n                >{index + 1}</div>\r\n              );\r\n            })}\r\n          </div>\r\n        </div>\r\n\r\n        <div\r\n          className='verify-bar-area'\r\n          style={{\r\n            width: setSize.imgWidth,\r\n            color: this.state.barAreaColor,\r\n            borderColor: this.state.barAreaBorderColor,\r\n            lineHeight: barSize.height,\r\n          }}\r\n        >\r\n          <span className='verify-msg'>{this.state.text}</span>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n}\r\n\r\nVerifyPoints.defaultProps = {\r\n  mode: 'fixed',\r\n  vSpace: 5,\r\n  imgSize: {\r\n    width: '310px',\r\n    height: '200px',\r\n  },\r\n  barSize: {\r\n    width: '310px',\r\n    height: '40px',\r\n  },\r\n  setSize: {\r\n    imgHeight: 200,\r\n    imgWidth: 310,\r\n    barHeight: 0,\r\n    barWidth: 0,\r\n  },\r\n};\r\n\r\nexport default VerifyPoints\r\n", "C:\\Users\\<USER>\\Desktop\\my-app\\src\\api\\base.js", [], "C:\\Users\\<USER>\\Desktop\\my-app\\src\\api\\axios.js", [], "C:\\Users\\<USER>\\Desktop\\my-app\\src\\components\\verifyPointFixed.js", ["113", "114", "115", "116", "117", "118", "119", "120", "121"], "C:\\Users\\<USER>\\Desktop\\my-app\\src\\components\\verifySlide.js", ["122", "123", "124", "125", "126", "127", "128", "129", "130", "131", "132", "133", "134", "135", "136", "137", "138", "139", "140", "141", "142", "143"], "import React, { Component } from 'react'\r\nimport { getPicture, reqCheck} from '../api/base.js'\r\nimport { CSSTransition } from 'react-transition-group';\r\nimport '../assets/index.css';\r\nimport {aesEncrypt} from \"../api/ase.js\";\r\n\r\nclass VerifySlide extends Component{\r\n  constructor(props) {\r\n    super(props)\r\n    this.state = {\r\n      blockSize: {\r\n        width: '50px',\r\n        height: '50px'\r\n      },\r\n      setSize: {\r\n        imgHeight: 200,\r\n        imgWidth: 310,\r\n        barHeight: 40,\r\n        barWidth: 310,\r\n      },\r\n      backImgBase: '', // 验证码背景图片\r\n      blockBackImgBase: '', // 验证滑块的背景图片\r\n      backToken: '', // 后端返回的唯一token值\r\n      startMoveTime:\"\",    //移动开始的时间\r\n      endMovetime:'',      //移动结束的时间\r\n      tipsBackColor:'',    //提示词的背景颜色\r\n      secretKey: '', //后端返回的加密秘钥 字段\r\n      captchaType: 'blockPuzzle',\r\n      moveBlockBackgroundColor: 'rgb(255, 255, 255)',\r\n      leftBarBorderColor: '',\r\n      iconColor: '',\r\n      barAreaLeft: 0,\r\n      barAreaOffsetWidth: 0,\r\n      startLeft: null,\r\n      moveBlockLeft: null,\r\n      leftBarWidth: null,\r\n      status: false,\t    //鼠标状态\r\n      isEnd: false,\t\t//是够验证完成\r\n      passFlag: '',\r\n      tipWords: '',\r\n      text: '向右滑动完成验证',\r\n    }\r\n\r\n  }\r\n  \r\n  componentDidMount() {\r\n    this.getData()\r\n    this.init()\r\n  }\r\n  init () {\r\n    var _this = this\r\n\r\n    window.removeEventListener(\"touchmove\", function (e) {\r\n        _this.move(e);\r\n    });\r\n    window.removeEventListener(\"mousemove\", function (e) {\r\n        _this.move(e);\r\n    });\r\n\r\n    //鼠标松开\r\n    window.removeEventListener(\"touchend\", function () {\r\n        _this.end();\r\n    });\r\n    window.removeEventListener(\"mouseup\", function () {\r\n        _this.end();\r\n    });\r\n\r\n    window.addEventListener(\"touchmove\", function (e) {\r\n        _this.move(e);\r\n    });\r\n    window.addEventListener(\"mousemove\", function (e) {\r\n        _this.move(e);\r\n    });\r\n\r\n    //鼠标松开\r\n    window.addEventListener(\"touchend\", function () {\r\n        _this.end();\r\n    });\r\n    window.addEventListener(\"mouseup\", function () {\r\n        _this.end();\r\n    });\r\n  }\r\n  getData() {\r\n    getPicture({captchaType: this.state.captchaType}).then(res => {\r\n      console.log(res)\r\n      if(res.repCode == '0000') {\r\n        this.setState({\r\n          backImgBase: res.repData.originalImageBase64,\r\n          blockBackImgBase: res.repData.jigsawImageBase64,\r\n          backToken: res.repData.token,\r\n          secretKey: res.repData.secretKey\r\n        })\r\n      }\r\n    })\r\n  }\r\n  refresh = () => {\r\n    this.getData()\r\n    this.setState({\r\n      moveBlockLeft: '',\r\n      leftBarWidth: '',\r\n      text: '向右滑动完成验证',\r\n      moveBlockBackgroundColor: '#fff',\r\n      leftBarBorderColor: '#337AB7',\r\n      iconColor: '#fff',\r\n      status: false,\r\n      isEnd: false\r\n    })\r\n  }\r\n  setBarArea = (event) => {\r\n    let barAreaLeft = event && event.getBoundingClientRect().left\r\n    let barAreaOffsetWidth = event && event.offsetWidth\r\n    this.state.barAreaLeft = barAreaLeft\r\n    this.state.barAreaOffsetWidth = barAreaOffsetWidth\r\n  }\r\n\r\n  start = (e) => {\r\n    e = e || window.event\r\n    if (!e.touches) {  //兼容PC端 \r\n        var x = e.clientX;\r\n    } else {           //兼容移动端\r\n        var x = e.touches[0].pageX;\r\n    }\r\n    this.state.startLeft =Math.floor(x - this.state.barAreaLeft);\r\n    this.state.startMoveTime = +new Date();    //开始滑动的时间\r\n    if (this.state.isEnd == false) {\r\n        this.setState({\r\n          text: '',\r\n          moveBlockBackgroundColor: '#337ab7',\r\n          leftBarBorderColor: '#337AB7',\r\n          iconColor: '#fff',\r\n          status: true\r\n        })\r\n        this.text = ''\r\n        e.stopPropagation();\r\n    }\r\n  }\r\n\r\n  move = (e) => {\r\n    e = e || window.event;\r\n    if (this.state.status && this.state.isEnd == false) {\r\n      if (!e.touches) {\r\n        //兼容PC端\r\n        var x = e.clientX;\r\n      } else {\r\n        //兼容移动端\r\n        var x = e.touches[0].pageX;\r\n      }\r\n      var bar_area_left = this.state.barAreaLeft;\r\n      var move_block_left = x - bar_area_left; //小方块相对于父元素的left值\r\n      if (move_block_left >= this.state.barAreaOffsetWidth - parseInt(parseInt(this.state.blockSize.width) / 2) - 2) {\r\n        move_block_left = this.state.barAreaOffsetWidth - parseInt(parseInt(this.state.blockSize.width) / 2) - 2;\r\n      }\r\n      if (move_block_left <= 0) {\r\n        move_block_left = parseInt(this.state.blockSize.width / 2);\r\n      }\r\n      //拖动后小方块的left值\r\n      this.state.moveBlockLeft = (move_block_left - this.state.startLeft) + \"px\"\r\n      this.state.leftBarWidth = (move_block_left - this.state.startLeft) + \"px\"\r\n      this.setState({\r\n        moveBlockLeft: this.state.moveBlockLeft,\r\n        leftBarWidth: this.state.leftBarWidth\r\n      })\r\n    } \r\n  }\r\n\r\n  end = () => {\r\n    this.state.endMovetime = +new Date(); \r\n    var _this = this;\r\n    //判断是否重合\r\n    if (this.state.status && this.state.isEnd == false) {\r\n        var moveLeftDistance = parseInt((this.state.moveBlockLeft || '').replace('px', ''));\r\n        moveLeftDistance = moveLeftDistance * 310/ parseInt(this.state.setSize.imgWidth)\r\n        let data = {\r\n            captchaType:this.state.captchaType,\r\n            \"pointJson\":this.state.secretKey ? aesEncrypt(JSON.stringify({x:moveLeftDistance,y:5.0}),this.state.secretKey):JSON.stringify({x:moveLeftDistance,y:5.0}),\r\n            \"token\":this.state.backToken\r\n        }\r\n        reqCheck(data).then(res=>{\r\n          if (res.repCode == \"0000\") {\r\n            this.state.isEnd = true;  \r\n            this.state.passFlag = true\r\n            this.state.tipWords = \r\n            this.setState({\r\n              tipWords: `${((this.state.endMovetime-this.state.startMoveTime)/1000).toFixed(2)}s验证成功`\r\n            })\r\n            setTimeout(() => {\r\n              this.state.tipWords = \"\"\r\n              this.refresh();\r\n            }, 1000)\r\n          } else {\r\n            this.setState({\r\n              isEnd: true,\r\n              moveBlockBackgroundColor: '#d9534f',\r\n              leftBarBorderColor: '#d9534f',\r\n              iconColor: '#fff',\r\n              iconClass: 'icon-close',\r\n              passFlag: false,\r\n              tipWords: '验证失败'\r\n            })\r\n            setTimeout(() => {\r\n              this.refresh();\r\n              this.setState({\r\n                tipWords: ''\r\n              })\r\n            }, 1000);\r\n          }\r\n        })\r\n        this.state.status = false;\r\n    }\r\n  }\r\n\r\n  render() {\r\n    const { mode, captchaType, vSpace, barSize, showRefresh,transitionWidth,finishText,transitionLeft} = this.props;\r\n    return (\r\n      <div style={{ position: 'relative' }} className='stop-user-select'>\r\n        <div\r\n          className='verify-img-out'\r\n          style={{ height: parseInt(this.state.setSize.imgHeight) + vSpace }}\r\n        >\r\n          <div\r\n            className='verify-img-panel'\r\n            style={{ width: this.state.setSize.imgWidth, height: this.state.setSize.imgHeight }}\r\n          >\r\n            <img\r\n              src={'data:image/png;base64,' + this.state.backImgBase}\r\n              alt=\"\"\r\n              style={{ width: '100%', height: '100%', display: 'block' }}\r\n            />\r\n            <div\r\n              className='verify-refresh'\r\n              onClick={() => this.refresh()}\r\n              style={{ display: showRefresh ? 'block' : 'none' }}\r\n            >\r\n              <i className='iconfont icon-refresh'></i>\r\n            </div>\r\n            <CSSTransition in={this.state.tipWords.length > 0} timeout={150} classNames=\"tips\" unmountOnExit>\r\n              <span\r\n                className={\r\n                  this.state.passFlag\r\n                    ? `${'verify-tips'} ${'suc-bg'}`\r\n                    : `${'verify-tips'} ${'err-bg'}`\r\n                }\r\n              >\r\n                {this.state.tipWords}\r\n              </span>\r\n            </CSSTransition>\r\n          </div>\r\n        </div>\r\n\r\n        <div\r\n          className='verify-bar-area'\r\n          style={{ width: this.state.setSize.imgWidth, height: barSize.height, lineHeight: barSize.height }}\r\n          ref={(bararea) => this.setBarArea(bararea)}\r\n        >\r\n          <span className='verify-msg'>{this.state.text}</span>\r\n          <div\r\n            className='verify-left-bar'\r\n            style={{\r\n              width: this.state.leftBarWidth !== undefined ? this.state.leftBarWidth : barSize.height,\r\n              height: barSize.height,\r\n              borderColor: this.state.leftBarBorderColor,\r\n              transaction: transitionWidth,\r\n            }}\r\n          >\r\n            <span className='verify-msg'>{finishText}</span>\r\n           \r\n            <div\r\n              className='verify-move-block'\r\n              onTouchStart={(e) => this.start(e)}\r\n              onMouseDown={(e) => this.start(e)}\r\n              style={{\r\n                width: barSize.height,\r\n                height: barSize.height,\r\n                backgroundColor: this.state.moveBlockBackgroundColor,\r\n                left: this.state.moveBlockLeft,\r\n                transition: transitionLeft,\r\n              }}\r\n            >\r\n              <i\r\n                className='verify-icon iconfont icon-right'\r\n                style={{ color: this.state.iconColor }}\r\n              ></i>\r\n              <div\r\n                className='verify-sub-block'\r\n                style={{\r\n                  width: Math.floor((parseInt(this.state.setSize.imgWidth) * 47) / 310) + 'px',\r\n                  height: this.state.setSize.imgHeight,\r\n                  top: '-' + (parseInt(this.state.setSize.imgHeight) + vSpace) + 'px',\r\n                  backgroundSize: this.state.setSize.imgWidth + ' ' + this.state.setSize.imgHeight,\r\n                }}\r\n              >\r\n                <img\r\n                  src={'data:image/png;base64,' + this.state.blockBackImgBase}\r\n                  alt=\"\"\r\n                  style={{ width: '100%', height: '100%', display: 'block' }}\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    )\r\n  }\r\n}\r\n\r\nVerifySlide.defaultProps = {\r\n  mode: 'fixed',\r\n  vSpace: 5,\r\n  imgSize: {\r\n    width: '310px',\r\n    height: '200px',\r\n  },\r\n  barSize: {\r\n    width: '310px',\r\n    height: '40px',\r\n  },\r\n  setSize: {\r\n    imgHeight: 200,\r\n    imgWidth: 310,\r\n    barHeight: 0,\r\n    barWidth: 0,\r\n  }\r\n};\r\n\r\nexport default VerifySlide", "C:\\Users\\<USER>\\Desktop\\my-app\\src\\components\\verifySlideFixed.js", ["144", "145", "146", "147", "148", "149", "150", "151", "152", "153", "154", "155", "156", "157", "158", "159", "160", "161", "162", "163"], "D:\\代码库\\行为验证码\\captcha\\view\\react-native\\src\\index.js", [], "D:\\代码库\\行为验证码\\captcha\\view\\react-native\\src\\App.js", [], "D:\\代码库\\行为验证码\\captcha\\view\\react-native\\src\\reportWebVitals.js", [], "D:\\代码库\\行为验证码\\captcha\\view\\react-native\\src\\Pages\\index.js", [], "D:\\代码库\\行为验证码\\captcha\\view\\react-native\\src\\components\\verifySlide.js", ["164", "165", "166", "167", "168", "169", "170", "171", "172", "173", "174", "175", "176", "177", "178", "179", "180", "181", "182", "183", "184", "185", "186", "187", "188"], "D:\\代码库\\行为验证码\\captcha\\view\\react-native\\src\\components\\verifyPointFixed.js", ["189", "190", "191", "192", "193", "194"], "import React, { Component } from 'react'\r\nimport { getPicture, reqCheck} from '../api/base.js'\r\nimport '../assets/index.css';\r\nimport defaultImg from './../assets/images/default.jpg'\r\nimport {aesEncrypt} from \"../api/ase.js\";\r\n\r\nclass VerifyPointFixed extends Component {\r\n  constructor(props) {\r\n    super(props);\r\n    this.state = {\r\n      secretKey: '', //后端返回的ase加密秘钥\r\n      checkNum: 3, //默认需要点击的字数\r\n      fontPos: [], //选中的坐标信息\r\n      checkPosArr: [], //用户点击的坐标\r\n      num: 1, //点击的记数\r\n      pointBackImgBase: '', //后端获取到的背景图片\r\n      poinTextList: [], //后端返回的点击字体顺序\r\n      backToken: '', //后端返回的token值\r\n      captchaType: 'clickWord',\r\n      setSize: {\r\n        imgHeight: 0,\r\n        imgWidth: 0,\r\n        barHeight: 0,\r\n        barWidth: 0,\r\n      },\r\n      tempPoints: [],\r\n      text: '',\r\n      barAreaColor: 'rgb(0,0,0)',\r\n      barAreaBorderColor: 'rgb(221, 221, 221)',\r\n      showRefresh: true,\r\n      bindingClick: true,\r\n    };\r\n  }\r\n  componentDidMount() {\r\n    this.uuid()\r\n    console.log(this.props)\r\n    this.getData()\r\n  }\r\n  // 初始话 uuid \r\n  uuid() {\r\n    var s = [];\r\n    var hexDigits = \"0123456789abcdef\";\r\n    for (var i = 0; i < 36; i++) {\r\n        s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);\r\n    }\r\n    s[14] = \"4\"; // bits 12-15 of the time_hi_and_version field to 0010\r\n    s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1); // bits 6-7 of the clock_seq_hi_and_reserved to 01\r\n    s[8] = s[13] = s[18] = s[23] = \"-\";\r\n\r\n    var slider = 'slider'+ '-'+s.join(\"\");\r\n    var point = 'point'+ '-'+s.join(\"\");\r\n    // 判断下是否存在 slider\r\n    console.log(localStorage.getItem('slider'))\r\n    if(!localStorage.getItem('slider')) {\r\n      localStorage.setItem('slider', slider)\r\n    }\r\n    if(!localStorage.getItem('point')) {\r\n      localStorage.setItem(\"point\",point);\r\n    }\r\n  }\r\n  // 刷新\r\n  refresh = () => {\r\n    this.getData()\r\n    this.setState({\r\n      num: 1,\r\n      tempPoints: [],\r\n      bindingClick: true,\r\n      barAreaColor: 'rgb(0,0,0)',\r\n      barAreaBorderColor: 'rgb(221, 221, 221)',\r\n    })\r\n  }\r\n  // 初始化数据\r\n  getData() {\r\n    getPicture({captchaType: this.state.captchaType,clientUid: localStorage.getItem('point'),ts: Date.now()}).then(res => {\r\n      if(res.repCode === '0000') {\r\n        this.setState({\r\n          pointBackImgBase: res.repData.originalImageBase64,\r\n          backToken: res.repData.token,\r\n          secretKey: res.repData.secretKey,\r\n          text: '请依次点击【' + res.repData.wordList + '】'\r\n        })\r\n      }\r\n      // 请求次数超限\r\n      if(res.repCode == '6201') {\r\n        this.setState({\r\n          pointBackImgBase: null,\r\n          text: res.repMsg,\r\n          barAreaColor: '#d9534f',\r\n          barAreaBorderColor: '#d9534f'\r\n        })\r\n      }\r\n    })\r\n  }\r\n  \r\n  canvasClick = (e) => {\r\n    if(this.state.bindingClick) {\r\n      this.state.tempPoints.push(this.getMousePos(e))\r\n      this.setState({\r\n        tempPoints: this.state.tempPoints\r\n      })\r\n      if(this.state.num === this.state.checkNum) {\r\n        this.setState({\r\n          bindingClick: false\r\n        })\r\n        let data = {\r\n          captchaType:this.state.captchaType,\r\n          \"pointJson\":this.state.secretKey? aesEncrypt(JSON.stringify(this.state.tempPoints),this.state.secretKey):JSON.stringify(this.state.tempPoints),\r\n          \"token\":this.state.backToken,\r\n          clientUid: localStorage.getItem('point'),\r\n          ts: Date.now()\r\n        }\r\n        reqCheck(data).then(res => {\r\n          if(res.repCode === '0000') {\r\n            this.setState({\r\n              text: '验证成功',\r\n              barAreaColor: '#4cae4c',\r\n              barAreaBorderColor: '#5cb85c'\r\n            })\r\n            setTimeout(() => {\r\n              this.refresh()\r\n            }, 1500)\r\n          } else {\r\n            this.setState({\r\n              text: res.repMsg,\r\n              barAreaColor: '#d9534f',\r\n              barAreaBorderColor: '#d9534f'\r\n            })\r\n            setTimeout(() => {\r\n                this.refresh();\r\n            }, 1000);\r\n          }\r\n        })\r\n      }\r\n      if(this.state.num < this.state.checkNum) {\r\n        this.createPoint(this.getMousePos(e))\r\n        this.setState({\r\n          num: this.state.num++\r\n        })\r\n      } \r\n    }\r\n  }\r\n   //获取坐标\r\n  getMousePos =(e) => {\r\n    var x = e.nativeEvent.offsetX\r\n    var y = e.nativeEvent.offsetY\r\n    return {x, y}\r\n  }\r\n  // 创建坐标点\r\n  createPoint = () => {\r\n    let num = this.state.num++\r\n    this.setState({\r\n      num\r\n    })\r\n  }\r\n\r\n  //坐标转换函数\r\n  pointTransfrom = (pointArr,imgSize) => {\r\n    var newPointArr = pointArr.map(p=>{\r\n        let x = Math.round(310 * p.x/parseInt(imgSize.imgWidth)) \r\n        let y =Math.round(155 * p.y/parseInt(imgSize.imgHeight)) \r\n        return {x,y}\r\n    })\r\n    // console.log(newPointArr,\"newPointArr\");\r\n    return newPointArr\r\n  }\r\n  \r\n  closeBox = () => {\r\n    this.props.verifyPointFixedChild(false)\r\n  }\r\n  \r\n  render() {\r\n    let tempPoints = this.state.tempPoints\r\n    const { vSpace, imgSize, barSize, setSize, isPointShow  } = this.props;\r\n    return (\r\n      // 蒙层\r\n      <div className='mask' style={{ display: isPointShow ? 'block' : 'none' }}>\r\n      <div className='verifybox' style={{ maxWidth: parseInt(imgSize.width) + 30 + 'px' }}>\r\n        <div className='verifybox-top'>\r\n          请完成安全验证\r\n          <span className='verifybox-close' onClick={() => this.closeBox()}>\r\n            <i className='iconfont icon-close'></i>\r\n          </span>\r\n        </div>\r\n        <div className='verifybox-bottom' style={{padding:'15px'}}>\r\n          {/* 验证容器 */}\r\n          <div style={{ position: 'relative' }}>\r\n            <div className='verify-img-out'>\r\n              <div\r\n                className='verify-img-panel'\r\n                style={{\r\n                  width: setSize.imgWidth + 'px',\r\n                  height: setSize.imgHeight + 'px',\r\n                  backgroundSize: setSize.imgWidth + 'px' + ' ' + setSize.imgHeight + 'px',\r\n                  marginBottom: vSpace + 'px',\r\n                }}\r\n              >\r\n                <div className='verify-refresh' style={{ zIndex: 3 }} onClick={this.refresh}>\r\n                  <i className='iconfont icon-refresh'></i>\r\n                </div>\r\n                {this.state.pointBackImgBase?\r\n            <img src={'data:image/png;base64,' + this.state.pointBackImgBase} alt=\"\" style={{width:'100%',height:'100%',display:'block'}} onClick={($event) => this.canvasClick($event)}/>:<img src={defaultImg} alt=\"\" style={{width:'100%',height:'100%',display:'block'}}/>}\r\n                {tempPoints.map((tempPoint, index) => {\r\n                  return (\r\n                    <div\r\n                      key={index}\r\n                      className=\"point-area\"\r\n                      style={{\r\n                        backgroundColor: '#1abd6c',\r\n                        color: '#fff',\r\n                        zIndex: 9999,\r\n                        width: '20px',\r\n                        height: '20px',\r\n                        textAlign: 'center',\r\n                        lineHeight: '20px',\r\n                        borderRadius: '50%',\r\n                        position: 'absolute',\r\n                        top: parseInt(tempPoint.y - 10) + 'px',\r\n                        left: parseInt(tempPoint.x - 10) + 'px',\r\n                        overflow:'hidden'\r\n                      }}\r\n                    >{index + 1}</div>\r\n                  );\r\n                })}\r\n              </div>\r\n            </div>\r\n\r\n            <div\r\n              className='verify-bar-area'\r\n              style={{\r\n                width: setSize.imgWidth,\r\n                color: this.state.barAreaColor,\r\n                borderColor: this.state.barAreaBorderColor,\r\n                lineHeight: barSize.height,\r\n              }}\r\n            >\r\n              <span className='verify-msg'>{this.state.text}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    );\r\n  }\r\n}\r\n\r\nVerifyPointFixed.defaultProps = {\r\n  mode: 'fixed',\r\n  vSpace: 5,\r\n  imgSize: {\r\n    width: '310px',\r\n    height: '200px',\r\n  },\r\n  barSize: {\r\n    width: '310px',\r\n    height: '40px',\r\n  },\r\n  setSize: {\r\n    imgHeight: 200,\r\n    imgWidth: 310,\r\n    barHeight: 0,\r\n    barWidth: 0,\r\n  },\r\n};\r\n\r\nexport default VerifyPointFixed\r\n", ["195", "196"], "D:\\代码库\\行为验证码\\captcha\\view\\react-native\\src\\components\\verifySlideFixed.js", ["197", "198", "199", "200", "201", "202", "203", "204", "205", "206", "207", "208", "209", "210", "211", "212", "213", "214", "215", "216", "217", "218", "219", "220"], "D:\\代码库\\行为验证码\\captcha\\view\\react-native\\src\\components\\verifyPoint.js", ["221", "222", "223", "224", "225", "226"], "import React, { Component } from 'react'\r\nimport { getPicture, reqCheck} from '../api/base.js'\r\nimport '../assets/index.css';\r\nimport defaultImg from './../assets/images/default.jpg'\r\nimport {aesEncrypt} from \"../api/ase.js\";\r\n\r\nclass VerifyPoints extends Component {\r\n  constructor(props) {\r\n    super(props);\r\n    this.state = {\r\n      secretKey: '', //后端返回的ase加密秘钥\r\n      checkNum: 3, //默认需要点击的字数\r\n      fontPos: [], //选中的坐标信息\r\n      checkPosArr: [], //用户点击的坐标\r\n      num: 1, //点击的记数\r\n      pointBackImgBase: '', //后端获取到的背景图片\r\n      poinTextList: [], //后端返回的点击字体顺序\r\n      backToken: '', //后端返回的token值\r\n      captchaType: 'clickWord',\r\n      setSize: {\r\n        imgHeight: 0,\r\n        imgWidth: 0,\r\n        barHeight: 0,\r\n        barWidth: 0,\r\n      },\r\n      tempPoints: [],\r\n      text: '',\r\n      barAreaColor: 'rgb(0,0,0)',\r\n      barAreaBorderColor: 'rgb(221, 221, 221)',\r\n      showRefresh: true,\r\n      bindingClick: true,\r\n    };\r\n  }\r\n  componentDidMount() {\r\n    this.uuid()\r\n    this.getData()\r\n  }\r\n  // 刷新\r\n  refresh = () => {\r\n    this.getData()\r\n    this.setState({\r\n      num: 1,\r\n      tempPoints: [],\r\n      bindingClick: true,\r\n      barAreaColor: 'rgb(0,0,0)',\r\n      barAreaBorderColor: 'rgb(221, 221, 221)',\r\n    })\r\n  }\r\n \t// 初始话 uuid \r\n  uuid() {\r\n    var s = [];\r\n    var hexDigits = \"0123456789abcdef\";\r\n    for (var i = 0; i < 36; i++) {\r\n        s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);\r\n    }\r\n    s[14] = \"4\"; // bits 12-15 of the time_hi_and_version field to 0010\r\n    s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1); // bits 6-7 of the clock_seq_hi_and_reserved to 01\r\n    s[8] = s[13] = s[18] = s[23] = \"-\";\r\n\r\n    var slider = 'slider'+ '-'+s.join(\"\");\r\n    var point = 'point'+ '-'+s.join(\"\");\r\n    // 判断下是否存在 slider\r\n    console.log(localStorage.getItem('slider'))\r\n    if(!localStorage.getItem('slider')) {\r\n      localStorage.setItem('slider', slider)\r\n    }\r\n    if(!localStorage.getItem('point')) {\r\n      localStorage.setItem(\"point\",point);\r\n    }\r\n  }\r\n  // 初始化数据\r\n  getData() {\r\n    getPicture({captchaType: this.state.captchaType, \tclientUid: localStorage.getItem('point'), ts: Date.now()}).then(res => {\r\n      if(res.repCode === '0000') {\r\n        this.setState({\r\n          pointBackImgBase: res.repData.originalImageBase64,\r\n          backToken: res.repData.token,\r\n          secretKey: res.repData.secretKey,\r\n          text: '请依次点击【' + res.repData.wordList + '】'\r\n        })\r\n      } else {\r\n        this.setState({\r\n          text: res.repMsg,\r\n          barAreaColor: '#d9534f',\r\n          barAreaBorderColor: '#d9534f'\r\n        })\r\n      }\r\n\r\n      // 请求次数超限\r\n      if(res.repCode == '6201') {\r\n        this.setState({\r\n          pointBackImgBase: null,\r\n          text: res.repMsg,\r\n          barAreaColor: '#d9534f',\r\n          barAreaBorderColor: '#d9534f'\r\n        })\r\n      }\r\n    })\r\n  }\r\n  \r\n  canvasClick = (e) => {\r\n    if(this.state.bindingClick) {\r\n      this.state.tempPoints.push(this.getMousePos(e))\r\n      this.setState({\r\n        tempPoints: this.state.tempPoints\r\n      })\r\n      if(this.state.num === this.state.checkNum) {\r\n        this.setState({\r\n          bindingClick: false\r\n        })\r\n        let data = {\r\n          captchaType:this.state.captchaType,\r\n          \"pointJson\":this.state.secretKey? aesEncrypt(JSON.stringify(this.state.tempPoints),this.state.secretKey):JSON.stringify(this.state.tempPoints),\r\n          \"token\":this.state.backToken,\r\n          clientUid: localStorage.getItem('point'),\r\n          ts: Date.now()\r\n        }\r\n        reqCheck(data).then(res => {\r\n          if(res.repCode === '0000') {\r\n            this.setState({\r\n              text: '验证成功',\r\n              barAreaColor: '#4cae4c',\r\n              barAreaBorderColor: '#5cb85c'\r\n            })\r\n            setTimeout(() => {\r\n              this.refresh()\r\n            }, 1500)\r\n          } else {\r\n            this.setState({\r\n              text: res.repMsg,\r\n              barAreaColor: '#d9534f',\r\n              barAreaBorderColor: '#d9534f'\r\n            })\r\n            setTimeout(() => {\r\n                this.refresh();\r\n            }, 1000);\r\n          }\r\n        })\r\n      }\r\n      if(this.state.num < this.state.checkNum) {\r\n        this.createPoint(this.getMousePos(e))\r\n        this.setState({\r\n          num: this.state.num++\r\n        })\r\n      } \r\n    }\r\n  }\r\n   //获取坐标\r\n  getMousePos =(e) => {\r\n    var x = e.nativeEvent.offsetX\r\n    var y = e.nativeEvent.offsetY\r\n    return {x, y}\r\n  }\r\n  // 创建坐标点\r\n  createPoint = () => {\r\n    let num = this.state.num++\r\n    this.setState({\r\n      num\r\n    })\r\n  }\r\n\r\n  //坐标转换函数\r\n  pointTransfrom = (pointArr,imgSize) => {\r\n    var newPointArr = pointArr.map(p=>{\r\n        let x = Math.round(310 * p.x/parseInt(imgSize.imgWidth)) \r\n        let y =Math.round(155 * p.y/parseInt(imgSize.imgHeight)) \r\n        return {x,y}\r\n    })\r\n    // console.log(newPointArr,\"newPointArr\");\r\n    return newPointArr\r\n  }\r\n\r\n  render() {\r\n    let tempPoints = this.state.tempPoints\r\n    const { vSpace, barSize, setSize } = this.props;\r\n    return (\r\n      <div style={{ position: 'relative' }}>\r\n        <div className='verify-img-out'>\r\n          <div\r\n            className='verify-img-panel'\r\n            style={{\r\n              width: setSize.imgWidth + 'px',\r\n              height: setSize.imgHeight + 'px',\r\n              backgroundSize: setSize.imgWidth + 'px' + ' ' + setSize.imgHeight + 'px',\r\n              marginBottom: vSpace + 'px',\r\n            }}\r\n          >\r\n            <div className='verify-refresh' style={{ zIndex: 3 }} onClick={this.refresh}>\r\n              <i className='iconfont icon-refresh'></i>\r\n            </div>\r\n            {this.state.pointBackImgBase?\r\n            <img src={'data:image/png;base64,' + this.state.pointBackImgBase} alt=\"\" style={{width:'100%',height:'100%',display:'block'}} onClick={($event) => this.canvasClick($event)}/>:<img src={defaultImg} alt=\"\" style={{width:'100%',height:'100%',display:'block'}}/>}\r\n\r\n            {tempPoints.map((tempPoint, index) => {\r\n              return (\r\n                <div\r\n                  key={index}\r\n                  className=\"point-area\"\r\n                  style={{\r\n                    backgroundColor: '#1abd6c',\r\n                    color: '#fff',\r\n                    zIndex: 9999,\r\n                    width: '20px',\r\n                    height: '20px',\r\n                    textAlign: 'center',\r\n                    lineHeight: '20px',\r\n                    borderRadius: '50%',\r\n                    position: 'absolute',\r\n                    top: parseInt(tempPoint.y - 10) + 'px',\r\n                    left: parseInt(tempPoint.x - 10) + 'px',\r\n                    overflow:'hidden'\r\n                  }}\r\n                >{index + 1}</div>\r\n              );\r\n            })}\r\n          </div>\r\n        </div>\r\n\r\n        <div\r\n          className='verify-bar-area'\r\n          style={{\r\n            width: setSize.imgWidth,\r\n            color: this.state.barAreaColor,\r\n            borderColor: this.state.barAreaBorderColor,\r\n            lineHeight: barSize.height,\r\n          }}\r\n        >\r\n          <span className='verify-msg'>{this.state.text}</span>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n}\r\n\r\nVerifyPoints.defaultProps = {\r\n  mode: 'fixed',\r\n  vSpace: 5,\r\n  imgSize: {\r\n    width: '310px',\r\n    height: '200px',\r\n  },\r\n  barSize: {\r\n    width: '310px',\r\n    height: '40px',\r\n  },\r\n  setSize: {\r\n    imgHeight: 200,\r\n    imgWidth: 310,\r\n    barHeight: 0,\r\n    barWidth: 0,\r\n  },\r\n};\r\n\r\nexport default VerifyPoints\r\n", "D:\\代码库\\行为验证码\\captcha\\view\\react-native\\src\\api\\base.js", [], "D:\\代码库\\行为验证码\\captcha\\view\\react-native\\src\\api\\ase.js", [], "D:\\代码库\\行为验证码\\captcha\\view\\react-native\\src\\api\\axios.js", [], {"ruleId": "227", "replacedBy": "228"}, {"ruleId": "229", "replacedBy": "230"}, {"ruleId": "231", "severity": 1, "message": "232", "line": 50, "column": 22, "nodeType": "233", "messageId": "234", "endLine": 50, "endColumn": 24}, {"ruleId": "231", "severity": 1, "message": "232", "line": 67, "column": 25, "nodeType": "233", "messageId": "234", "endLine": 67, "endColumn": 27}, {"ruleId": "231", "severity": 1, "message": "232", "line": 77, "column": 26, "nodeType": "233", "messageId": "234", "endLine": 77, "endColumn": 28}, {"ruleId": "235", "severity": 1, "message": "236", "line": 101, "column": 16, "nodeType": "237", "endLine": 101, "endColumn": 26}, {"ruleId": "235", "severity": 1, "message": "236", "line": 114, "column": 15, "nodeType": "237", "endLine": 114, "endColumn": 25}, {"ruleId": "238", "severity": 1, "message": "239", "line": 133, "column": 13, "nodeType": "240", "messageId": "241", "endLine": 133, "endColumn": 17}, {"ruleId": "238", "severity": 1, "message": "242", "line": 133, "column": 19, "nodeType": "240", "messageId": "241", "endLine": 133, "endColumn": 30}, {"ruleId": "238", "severity": 1, "message": "243", "line": 133, "column": 40, "nodeType": "240", "messageId": "241", "endLine": 133, "endColumn": 47}, {"ruleId": "238", "severity": 1, "message": "244", "line": 133, "column": 67, "nodeType": "240", "messageId": "241", "endLine": 133, "endColumn": 83}, {"ruleId": "245", "severity": 1, "message": "246", "line": 142, "column": 55, "nodeType": "233", "messageId": "247", "endLine": 142, "endColumn": 56}, {"ruleId": "231", "severity": 1, "message": "232", "line": 51, "column": 22, "nodeType": "233", "messageId": "234", "endLine": 51, "endColumn": 24}, {"ruleId": "231", "severity": 1, "message": "232", "line": 68, "column": 25, "nodeType": "233", "messageId": "234", "endLine": 68, "endColumn": 27}, {"ruleId": "231", "severity": 1, "message": "232", "line": 78, "column": 26, "nodeType": "233", "messageId": "234", "endLine": 78, "endColumn": 28}, {"ruleId": "235", "severity": 1, "message": "236", "line": 102, "column": 16, "nodeType": "237", "endLine": 102, "endColumn": 26}, {"ruleId": "235", "severity": 1, "message": "236", "line": 115, "column": 15, "nodeType": "237", "endLine": 115, "endColumn": 25}, {"ruleId": "238", "severity": 1, "message": "239", "line": 138, "column": 13, "nodeType": "240", "messageId": "241", "endLine": 138, "endColumn": 17}, {"ruleId": "238", "severity": 1, "message": "242", "line": 138, "column": 19, "nodeType": "240", "messageId": "241", "endLine": 138, "endColumn": 30}, {"ruleId": "238", "severity": 1, "message": "244", "line": 138, "column": 67, "nodeType": "240", "messageId": "241", "endLine": 138, "endColumn": 83}, {"ruleId": "245", "severity": 1, "message": "246", "line": 158, "column": 59, "nodeType": "233", "messageId": "247", "endLine": 158, "endColumn": 60}, {"ruleId": "231", "severity": 1, "message": "232", "line": 86, "column": 22, "nodeType": "233", "messageId": "234", "endLine": 86, "endColumn": 24}, {"ruleId": "235", "severity": 1, "message": "236", "line": 112, "column": 5, "nodeType": "237", "endLine": 112, "endColumn": 15}, {"ruleId": "235", "severity": 1, "message": "236", "line": 113, "column": 5, "nodeType": "237", "endLine": 113, "endColumn": 15}, {"ruleId": "248", "severity": 1, "message": "249", "line": 121, "column": 13, "nodeType": "240", "messageId": "250", "endLine": 121, "endColumn": 14}, {"ruleId": "235", "severity": 1, "message": "236", "line": 123, "column": 5, "nodeType": "237", "endLine": 123, "endColumn": 15}, {"ruleId": "235", "severity": 1, "message": "236", "line": 124, "column": 5, "nodeType": "237", "endLine": 124, "endColumn": 15}, {"ruleId": "231", "severity": 1, "message": "232", "line": 125, "column": 26, "nodeType": "233", "messageId": "234", "endLine": 125, "endColumn": 28}, {"ruleId": "231", "severity": 1, "message": "232", "line": 140, "column": 47, "nodeType": "233", "messageId": "234", "endLine": 140, "endColumn": 49}, {"ruleId": "248", "severity": 1, "message": "249", "line": 146, "column": 13, "nodeType": "240", "messageId": "250", "endLine": 146, "endColumn": 14}, {"ruleId": "235", "severity": 1, "message": "236", "line": 157, "column": 7, "nodeType": "237", "endLine": 157, "endColumn": 17}, {"ruleId": "235", "severity": 1, "message": "236", "line": 158, "column": 7, "nodeType": "237", "endLine": 158, "endColumn": 17}, {"ruleId": "235", "severity": 1, "message": "236", "line": 167, "column": 5, "nodeType": "237", "endLine": 167, "endColumn": 15}, {"ruleId": "238", "severity": 1, "message": "251", "line": 168, "column": 9, "nodeType": "240", "messageId": "241", "endLine": 168, "endColumn": 14}, {"ruleId": "231", "severity": 1, "message": "232", "line": 170, "column": 47, "nodeType": "233", "messageId": "234", "endLine": 170, "endColumn": 49}, {"ruleId": "231", "severity": 1, "message": "232", "line": 179, "column": 27, "nodeType": "233", "messageId": "234", "endLine": 179, "endColumn": 29}, {"ruleId": "235", "severity": 1, "message": "236", "line": 180, "column": 13, "nodeType": "237", "endLine": 180, "endColumn": 23}, {"ruleId": "235", "severity": 1, "message": "236", "line": 181, "column": 13, "nodeType": "237", "endLine": 181, "endColumn": 23}, {"ruleId": "235", "severity": 1, "message": "236", "line": 182, "column": 13, "nodeType": "237", "endLine": 182, "endColumn": 23}, {"ruleId": "235", "severity": 1, "message": "236", "line": 187, "column": 15, "nodeType": "237", "endLine": 187, "endColumn": 25}, {"ruleId": "235", "severity": 1, "message": "236", "line": 208, "column": 9, "nodeType": "237", "endLine": 208, "endColumn": 19}, {"ruleId": "238", "severity": 1, "message": "239", "line": 213, "column": 13, "nodeType": "240", "messageId": "241", "endLine": 213, "endColumn": 17}, {"ruleId": "238", "severity": 1, "message": "242", "line": 213, "column": 19, "nodeType": "240", "messageId": "241", "endLine": 213, "endColumn": 30}, {"ruleId": "231", "severity": 1, "message": "232", "line": 86, "column": 22, "nodeType": "233", "messageId": "234", "endLine": 86, "endColumn": 24}, {"ruleId": "235", "severity": 1, "message": "236", "line": 112, "column": 5, "nodeType": "237", "endLine": 112, "endColumn": 15}, {"ruleId": "235", "severity": 1, "message": "236", "line": 113, "column": 5, "nodeType": "237", "endLine": 113, "endColumn": 15}, {"ruleId": "248", "severity": 1, "message": "249", "line": 121, "column": 13, "nodeType": "240", "messageId": "250", "endLine": 121, "endColumn": 14}, {"ruleId": "235", "severity": 1, "message": "236", "line": 123, "column": 5, "nodeType": "237", "endLine": 123, "endColumn": 15}, {"ruleId": "235", "severity": 1, "message": "236", "line": 124, "column": 5, "nodeType": "237", "endLine": 124, "endColumn": 15}, {"ruleId": "231", "severity": 1, "message": "232", "line": 125, "column": 26, "nodeType": "233", "messageId": "234", "endLine": 125, "endColumn": 28}, {"ruleId": "231", "severity": 1, "message": "232", "line": 140, "column": 47, "nodeType": "233", "messageId": "234", "endLine": 140, "endColumn": 49}, {"ruleId": "248", "severity": 1, "message": "249", "line": 146, "column": 13, "nodeType": "240", "messageId": "250", "endLine": 146, "endColumn": 14}, {"ruleId": "235", "severity": 1, "message": "236", "line": 157, "column": 7, "nodeType": "237", "endLine": 157, "endColumn": 17}, {"ruleId": "235", "severity": 1, "message": "236", "line": 158, "column": 7, "nodeType": "237", "endLine": 158, "endColumn": 17}, {"ruleId": "235", "severity": 1, "message": "236", "line": 167, "column": 5, "nodeType": "237", "endLine": 167, "endColumn": 15}, {"ruleId": "238", "severity": 1, "message": "251", "line": 168, "column": 9, "nodeType": "240", "messageId": "241", "endLine": 168, "endColumn": 14}, {"ruleId": "231", "severity": 1, "message": "232", "line": 170, "column": 47, "nodeType": "233", "messageId": "234", "endLine": 170, "endColumn": 49}, {"ruleId": "231", "severity": 1, "message": "232", "line": 179, "column": 27, "nodeType": "233", "messageId": "234", "endLine": 179, "endColumn": 29}, {"ruleId": "235", "severity": 1, "message": "236", "line": 180, "column": 13, "nodeType": "237", "endLine": 180, "endColumn": 23}, {"ruleId": "235", "severity": 1, "message": "236", "line": 181, "column": 13, "nodeType": "237", "endLine": 181, "endColumn": 23}, {"ruleId": "235", "severity": 1, "message": "236", "line": 182, "column": 13, "nodeType": "237", "endLine": 182, "endColumn": 23}, {"ruleId": "235", "severity": 1, "message": "236", "line": 187, "column": 15, "nodeType": "237", "endLine": 187, "endColumn": 25}, {"ruleId": "235", "severity": 1, "message": "236", "line": 208, "column": 9, "nodeType": "237", "endLine": 208, "endColumn": 19}, {"ruleId": "245", "severity": 1, "message": "246", "line": 63, "column": 26, "nodeType": "233", "messageId": "247", "endLine": 63, "endColumn": 27}, {"ruleId": "245", "severity": 1, "message": "246", "line": 64, "column": 24, "nodeType": "233", "messageId": "247", "endLine": 64, "endColumn": 25}, {"ruleId": "231", "severity": 1, "message": "232", "line": 118, "column": 22, "nodeType": "233", "messageId": "234", "endLine": 118, "endColumn": 24}, {"ruleId": "235", "severity": 1, "message": "236", "line": 152, "column": 5, "nodeType": "237", "endLine": 152, "endColumn": 15}, {"ruleId": "235", "severity": 1, "message": "236", "line": 153, "column": 5, "nodeType": "237", "endLine": 153, "endColumn": 15}, {"ruleId": "248", "severity": 1, "message": "249", "line": 161, "column": 13, "nodeType": "240", "messageId": "250", "endLine": 161, "endColumn": 14}, {"ruleId": "235", "severity": 1, "message": "236", "line": 163, "column": 5, "nodeType": "237", "endLine": 163, "endColumn": 15}, {"ruleId": "235", "severity": 1, "message": "236", "line": 164, "column": 5, "nodeType": "237", "endLine": 164, "endColumn": 15}, {"ruleId": "231", "severity": 1, "message": "232", "line": 165, "column": 26, "nodeType": "233", "messageId": "234", "endLine": 165, "endColumn": 28}, {"ruleId": "231", "severity": 1, "message": "232", "line": 180, "column": 47, "nodeType": "233", "messageId": "234", "endLine": 180, "endColumn": 49}, {"ruleId": "248", "severity": 1, "message": "249", "line": 186, "column": 13, "nodeType": "240", "messageId": "250", "endLine": 186, "endColumn": 14}, {"ruleId": "235", "severity": 1, "message": "236", "line": 197, "column": 7, "nodeType": "237", "endLine": 197, "endColumn": 17}, {"ruleId": "235", "severity": 1, "message": "236", "line": 198, "column": 7, "nodeType": "237", "endLine": 198, "endColumn": 17}, {"ruleId": "235", "severity": 1, "message": "236", "line": 207, "column": 5, "nodeType": "237", "endLine": 207, "endColumn": 15}, {"ruleId": "238", "severity": 1, "message": "251", "line": 208, "column": 9, "nodeType": "240", "messageId": "241", "endLine": 208, "endColumn": 14}, {"ruleId": "231", "severity": 1, "message": "232", "line": 210, "column": 47, "nodeType": "233", "messageId": "234", "endLine": 210, "endColumn": 49}, {"ruleId": "231", "severity": 1, "message": "232", "line": 221, "column": 27, "nodeType": "233", "messageId": "234", "endLine": 221, "endColumn": 29}, {"ruleId": "235", "severity": 1, "message": "236", "line": 222, "column": 13, "nodeType": "237", "endLine": 222, "endColumn": 23}, {"ruleId": "235", "severity": 1, "message": "236", "line": 223, "column": 13, "nodeType": "237", "endLine": 223, "endColumn": 23}, {"ruleId": "235", "severity": 1, "message": "236", "line": 224, "column": 13, "nodeType": "237", "endLine": 224, "endColumn": 23}, {"ruleId": "235", "severity": 1, "message": "236", "line": 229, "column": 15, "nodeType": "237", "endLine": 229, "endColumn": 25}, {"ruleId": "235", "severity": 1, "message": "236", "line": 250, "column": 9, "nodeType": "237", "endLine": 250, "endColumn": 19}, {"ruleId": "238", "severity": 1, "message": "239", "line": 255, "column": 13, "nodeType": "240", "messageId": "241", "endLine": 255, "endColumn": 17}, {"ruleId": "238", "severity": 1, "message": "242", "line": 255, "column": 19, "nodeType": "240", "messageId": "241", "endLine": 255, "endColumn": 30}, {"ruleId": "238", "severity": 1, "message": "252", "line": 255, "column": 49, "nodeType": "240", "messageId": "241", "endLine": 255, "endColumn": 60}, {"ruleId": "245", "severity": 1, "message": "246", "line": 50, "column": 26, "nodeType": "233", "messageId": "247", "endLine": 50, "endColumn": 27}, {"ruleId": "245", "severity": 1, "message": "246", "line": 51, "column": 24, "nodeType": "233", "messageId": "247", "endLine": 51, "endColumn": 25}, {"ruleId": "231", "severity": 1, "message": "232", "line": 84, "column": 22, "nodeType": "233", "messageId": "234", "endLine": 84, "endColumn": 24}, {"ruleId": "235", "severity": 1, "message": "236", "line": 137, "column": 16, "nodeType": "237", "endLine": 137, "endColumn": 26}, {"ruleId": "235", "severity": 1, "message": "236", "line": 150, "column": 15, "nodeType": "237", "endLine": 150, "endColumn": 25}, {"ruleId": "245", "severity": 1, "message": "246", "line": 193, "column": 59, "nodeType": "233", "messageId": "247", "endLine": 193, "endColumn": 60}, {"ruleId": "227", "replacedBy": "253"}, {"ruleId": "229", "replacedBy": "254"}, {"ruleId": "245", "severity": 1, "message": "246", "line": 63, "column": 26, "nodeType": "233", "messageId": "247", "endLine": 63, "endColumn": 27}, {"ruleId": "245", "severity": 1, "message": "246", "line": 64, "column": 24, "nodeType": "233", "messageId": "247", "endLine": 64, "endColumn": 25}, {"ruleId": "231", "severity": 1, "message": "232", "line": 110, "column": 22, "nodeType": "233", "messageId": "234", "endLine": 110, "endColumn": 24}, {"ruleId": "231", "severity": 1, "message": "232", "line": 119, "column": 22, "nodeType": "233", "messageId": "234", "endLine": 119, "endColumn": 24}, {"ruleId": "235", "severity": 1, "message": "236", "line": 153, "column": 5, "nodeType": "237", "endLine": 153, "endColumn": 15}, {"ruleId": "235", "severity": 1, "message": "236", "line": 154, "column": 5, "nodeType": "237", "endLine": 154, "endColumn": 15}, {"ruleId": "248", "severity": 1, "message": "249", "line": 162, "column": 13, "nodeType": "240", "messageId": "250", "endLine": 162, "endColumn": 14}, {"ruleId": "235", "severity": 1, "message": "236", "line": 164, "column": 5, "nodeType": "237", "endLine": 164, "endColumn": 15}, {"ruleId": "235", "severity": 1, "message": "236", "line": 165, "column": 5, "nodeType": "237", "endLine": 165, "endColumn": 15}, {"ruleId": "231", "severity": 1, "message": "232", "line": 166, "column": 26, "nodeType": "233", "messageId": "234", "endLine": 166, "endColumn": 28}, {"ruleId": "231", "severity": 1, "message": "232", "line": 181, "column": 47, "nodeType": "233", "messageId": "234", "endLine": 181, "endColumn": 49}, {"ruleId": "248", "severity": 1, "message": "249", "line": 187, "column": 13, "nodeType": "240", "messageId": "250", "endLine": 187, "endColumn": 14}, {"ruleId": "235", "severity": 1, "message": "236", "line": 198, "column": 7, "nodeType": "237", "endLine": 198, "endColumn": 17}, {"ruleId": "235", "severity": 1, "message": "236", "line": 199, "column": 7, "nodeType": "237", "endLine": 199, "endColumn": 17}, {"ruleId": "235", "severity": 1, "message": "236", "line": 208, "column": 5, "nodeType": "237", "endLine": 208, "endColumn": 15}, {"ruleId": "238", "severity": 1, "message": "251", "line": 209, "column": 9, "nodeType": "240", "messageId": "241", "endLine": 209, "endColumn": 14}, {"ruleId": "231", "severity": 1, "message": "232", "line": 211, "column": 47, "nodeType": "233", "messageId": "234", "endLine": 211, "endColumn": 49}, {"ruleId": "231", "severity": 1, "message": "232", "line": 222, "column": 27, "nodeType": "233", "messageId": "234", "endLine": 222, "endColumn": 29}, {"ruleId": "235", "severity": 1, "message": "236", "line": 223, "column": 13, "nodeType": "237", "endLine": 223, "endColumn": 23}, {"ruleId": "235", "severity": 1, "message": "236", "line": 224, "column": 13, "nodeType": "237", "endLine": 224, "endColumn": 23}, {"ruleId": "235", "severity": 1, "message": "236", "line": 225, "column": 13, "nodeType": "237", "endLine": 225, "endColumn": 23}, {"ruleId": "235", "severity": 1, "message": "236", "line": 230, "column": 15, "nodeType": "237", "endLine": 230, "endColumn": 25}, {"ruleId": "235", "severity": 1, "message": "236", "line": 251, "column": 9, "nodeType": "237", "endLine": 251, "endColumn": 19}, {"ruleId": "238", "severity": 1, "message": "252", "line": 260, "column": 30, "nodeType": "240", "messageId": "241", "endLine": 260, "endColumn": 41}, {"ruleId": "245", "severity": 1, "message": "246", "line": 60, "column": 26, "nodeType": "233", "messageId": "247", "endLine": 60, "endColumn": 27}, {"ruleId": "245", "severity": 1, "message": "246", "line": 61, "column": 24, "nodeType": "233", "messageId": "247", "endLine": 61, "endColumn": 25}, {"ruleId": "231", "severity": 1, "message": "232", "line": 90, "column": 22, "nodeType": "233", "messageId": "234", "endLine": 90, "endColumn": 24}, {"ruleId": "235", "severity": 1, "message": "236", "line": 143, "column": 16, "nodeType": "237", "endLine": 143, "endColumn": 26}, {"ruleId": "235", "severity": 1, "message": "236", "line": 156, "column": 15, "nodeType": "237", "endLine": 156, "endColumn": 25}, {"ruleId": "245", "severity": 1, "message": "246", "line": 184, "column": 55, "nodeType": "233", "messageId": "247", "endLine": 184, "endColumn": 56}, "no-native-reassign", ["255"], "no-negated-in-lhs", ["256"], "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", "react/no-direct-mutation-state", "Do not mutate state directly. Use setState().", "MemberExpression", "no-unused-vars", "'mode' is assigned a value but never used.", "Identifier", "unusedVar", "'captchaType' is assigned a value but never used.", "'imgSize' is assigned a value but never used.", "'pointBackImgBase' is assigned a value but never used.", "no-useless-concat", "Unexpected string concatenation of literals.", "unexpectedConcat", "no-redeclare", "'x' is already defined.", "redeclared", "'_this' is assigned a value but never used.", "'showRefresh' is assigned a value but never used.", ["255"], ["256"], "no-global-assign", "no-unsafe-negation"]