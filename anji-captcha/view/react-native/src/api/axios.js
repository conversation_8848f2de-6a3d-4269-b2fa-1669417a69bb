import axios from 'axios';
axios.defaults.baseURL = 'https://captcha.anji-plus.com/captcha-api';
// axios.defaults.baseURL = 'http://10.108.11.46:8088/';

const service = axios.create({
  withCredentials: true,
  timeout: 40000,
  headers: {
    'X-Requested-With': 'XMLHttpRequest',
    'Content-Type': 'application/json; charset=UTF-8'
  },
})

service.interceptors.request.use(
  config => {
    return config
  },
  error => {
    // Do something with request error
    // console.log(error) // for debug
    Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  response => {
    const res = response.data;
    return res;
    
  },
)

export default service
