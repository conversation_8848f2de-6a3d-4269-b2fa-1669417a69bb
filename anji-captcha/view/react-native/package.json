{"name": "my-app", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.11.6", "@testing-library/react": "^11.2.2", "@testing-library/user-event": "^12.5.0", "axios": "^0.21.0", "crypto-js": "^4.0.0", "dva": "^2.4.1", "react": "^17.0.1", "react-dom": "^17.0.1", "react-scripts": "4.0.1", "web-vitals": "^0.2.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"react-transition-group": "^4.4.1"}}