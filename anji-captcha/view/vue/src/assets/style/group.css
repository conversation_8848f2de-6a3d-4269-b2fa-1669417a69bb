.content {
	padding-bottom: 100px;
}
.bg-w {
	background: #fff;
}
.c-mt20 {
	margin-top: 20px;
}
.h2 {
	font-size: 16px;
	color: #333;
	line-height: 42px;
}
.content-border {
	border-top: 12px solid #f2f4fb;
}
.pd-content {
	padding: 12px 120px;
}
.pd-main {
  padding-left: 2.5rem;
  padding-right: 2.5rem;
}
@media screen and (max-width: 1420px) {
	.pd-content {
		padding: 12px 10px;
	}
}
@media only screen and (min-width: 1200px){
  .el-col-lg-22 {
    width: 99.66667%;
  }
  @media only screen and (min-width: 992px){
    .el-col-md-22 {
      width: 99.66667%;
    }
    @media only screen and (min-width: 768px){
      .el-col-sm-22 {
        width: 99.66667%;
      }
    }
  }
}
.mt10{
  margin-top: 10px;
}
.pt20 {
	padding-top: 20px;
}
.pt10 {
	padding-top: 10px;
}
.mb10{
  margin-bottom: 10px;
}
.pb20 {
	padding-bottom: 20px;
}
.pb50 {
	padding-bottom: 50px;
}
.pc-height {
	/*height: calc(100vh - 186px);*/
	/*height: -moz-calc(100vh - 186px);*/
	/*height: -webkit-calc(100vh - 186px);*/
	/*height: calc(100vh - 186px);*/
	/*overflow-y: auto;*/
	/*overflow-x: hidden;*/
}
/*搜索框，输入框，按钮*/
input::-webkit-input-placeholder {
	/* WebKit browsers */
	color: #e3e3e3;
}
input:-moz-placeholder {
	/* Mozilla Firefox 4 to 18 */
	color: #e3e3e3;
}
input::-moz-placeholder {
	/* Mozilla Firefox 19+ */
	color: #e3e3e3;
}
input:-ms-input-placeholder {
	/* Internet Explorer 10+ */
	color: #e3e3e3;
}
.c-input {
	background: #ffffff;
	border: 1px solid rgba(151, 151, 151, 0.2);
	border-radius: 100px;
	height: 24px;
	line-height: 31px;
	padding: 3px 30px  3px 10px ;
	/*min-width: 20vw;*/
	color: #333;
	font-size: 14px;
}
.c-search {
	position: relative;
  overflow: hidden;
}
.c-search-button.position {
	position: absolute;
	top: 0;
	right: 0;
}
.c-search-button {
	background: #7ab1f9;
	border-radius: 0 30px 30px 0;
	height: 31px;
	text-align: center;
	padding: 0 16px;
	border: 0;
	color: #fff;
}
.c-search-button:hover {
	color: #fff;
	background: #3395fb;
}
.c-button {
	border-radius: 100px;
	border: 0;
	height: 32px;
	color: #fff;
	line-height: 0;
	/*width: 90px;*/
	text-align: center;
}

.c-button-normal {
	border-radius: 100px;
	border: 0;
	height: 40px;
	color: #fff;
	width: 100px;
	text-align: center;
}
.c-button-mini {
	border-radius: 100px;
	border: 0;
	color: #fff;
	padding: 5px 12px;
	text-align: center;
}
.c-button.blue,
.c-button-normal.blue,
.c-button-mini.blue {
	background: #7ab1f9;
}
.c-button.green,
.c-button-normal.green,
.c-button-mini.green {
	background: #7ed321;
}
.c-button.red,
.c-button-normal.red,
.c-button-mini.red {
	background: #ff7272;
}
.c-button.yellow,
.c-button-normal.yellow,
.c-button-mini.yellow {
	background: #f6a93b;
}
.c-button.white,
.c-button-normal.white,
.c-button-mini.white {
	background: #fff;
	border: 1px solid #79b1f9;
	color: #79b1f9;
}
.c-button.purple,
.c-button-normal.purple,
.c-button-mini.purple {
  background: #e5bfa3;
}
.c-button.blue:hover,
.c-button-normal.blue:hover,
.c-button-mini.blue:hover {
	color: white;
	background: #3395fb;
}
.c-button.green:hover,
.c-button-normal.green:hover,
.c-button-mini.green:hover {
	color: white;
	background: #50d310;
}
.c-button.red:hover,
.c-button-normal.red:hover,
.c-button-mini.red:hover {
	color: white;
	background: #ff7260;
}
.c-button.yellow:hover,
.c-button-normal.yellow:hover,
.c-button-mini.yellow:hover {
	color: white;
	background: #f6b01d;
}
.c-button.white:hover,
.c-button-normal.white:hover,
.c-button-mini.white:hover {
	color: #fff;
	background: #79b1f9;
}
.c-button.purple:hover,
.c-button-normal.purple:hover,
.c-button-mini.purple:hover {
  color: #fff;
  background: #e5bfa3;
}
/*图标 按钮 隐藏按钮特性*/
.icon-btn_style {
	background: none;
	border: 0;
	padding: 0;
}
.icon-btn_style:hover {
	background: none;
	border: 0;
	padding: 0;
}
/*模态框 总体样式*/
.dialog {
	position: fixed;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	background: rgba(0, 0, 0, 0.6);
	z-index: 9999;
}
.dialog .dialog-container {
	width: 600px;
	height: 500px;
	background: #ffffff;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	border-radius: 8px;
	position: relative;
}
.dialog .dialog-title {
	width: 100%;
	height: 60px;
	font-size: 18px;
	color: #696969;
	font-weight: 600;
	padding: 16px 50px 0 20px;
	box-sizing: border-box;
}
.content {
	color: #797979;
	line-height: 26px;
	padding: 0 20px;
	box-sizing: border-box;
}
.inp {
	margin: 10px 0 0 20px;
	width: 200px;
	height: 40px;
	padding-left: 4px;
	border-radius: 4px;
	border: none;
	background: #efefef;
	outline: none;
}
.inp:focus {
	border: 1px solid #509ee3;
}
.btns {
	width: 100%;
	height: 60px;
	position: absolute;
	bottom: 0;
	left: 0;
	text-align: right;
	padding: 0 16px;
	box-sizing: border-box;
}

.btns > div {
	display: inline-block;
	height: 40px;
	line-height: 40px;
	padding: 0 14px;
	color: #ffffff;
	background: #f1f1f1;
	border-radius: 8px;
	margin-right: 12px;
	cursor: pointer;
}
.close-btn {
	position: absolute;
	top: 16px;
	right: 16px;
	width: 30px;
	height: 30px;
	line-height: 30px;
	text-align: center;
	font-size: 18px;
	cursor: pointer;
}
.close-btn:hover {
	font-weight: 600;
}
/*main*/
.search-pop {
	margin-top: 64px;
}
.table-input .el-input--mini .el-input__inner {
	height: 32px !important;
}
.see-data .el-input {
	width: auto !important;
}
.fr{
  float: right;
}
