//
//  CryptoSwift
//
//  Copyright (C) <PERSON><PERSON> <<EMAIL>>
//  This software is provided 'as-is', without any express or implied warranty.
//
//  In no event will the authors be held liable for any damages arising from the use of this software.
//
//  Permission is granted to anyone to use this software for any purpose,including commercial applications, and to alter it and redistribute it freely, subject to the following restrictions:
//
//  - The origin of this software must not be misrepresented; you must not claim that you wrote the original software. If you use this software in a product, an acknowledgment in the product documentation is required.
//  - Altered source versions must be plainly marked as such, and must not be misrepresented as being the original software.
//  - This notice may not be removed or altered from any source or binary distribution.
//

import Foundation

// First byte is 0x80, rest is zero padding
// http://www.crypto-it.net/eng/theory/padding.html
// http://www.embedx.com/pdfs/ISO_STD_7816/info_isoiec7816-4%7Bed21.0%7Den.pdf
struct ISO78164Padding: PaddingProtocol {
  init() {
  }

  func add(to bytes: Array<UInt8>, blockSize: Int) -> Array<UInt8> {
    var padded = Array(bytes)
    padded.append(0x80)

    while (padded.count % blockSize) != 0 {
      padded.append(0x00)
    }
    return padded
  }

  func remove(from bytes: Array<UInt8>, blockSize _: Int?) -> Array<UInt8> {
    if let idx = bytes.lastIndex(of: 0x80) {
      return Array(bytes[..<idx])
    } else {
      return bytes
    }
  }
}
