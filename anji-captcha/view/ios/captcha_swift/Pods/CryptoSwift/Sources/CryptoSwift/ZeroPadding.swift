//
//  CryptoSwift
//
//  Copyright (C) 2014-2017 <PERSON><PERSON> <<EMAIL>>
//  This software is provided 'as-is', without any express or implied warranty.
//
//  In no event will the authors be held liable for any damages arising from the use of this software.
//
//  Permission is granted to anyone to use this software for any purpose,including commercial applications, and to alter it and redistribute it freely, subject to the following restrictions:
//
//  - The origin of this software must not be misrepresented; you must not claim that you wrote the original software. If you use this software in a product, an acknowledgment in the product documentation is required.
//  - Altered source versions must be plainly marked as such, and must not be misrepresented as being the original software.
//  - This notice may not be removed or altered from any source or binary distribution.
//

/// All the bytes that are required to be padded are padded with zero.
/// Zero padding may not be reversible if the original file ends with one or more zero bytes.
struct ZeroPadding: PaddingProtocol {
  init() {
  }

  func add(to bytes: Array<UInt8>, blockSize: Int) -> Array<UInt8> {
    let paddingCount = blockSize - (bytes.count % blockSize)
    if paddingCount > 0 {
      return bytes + Array<UInt8>(repeating: 0, count: paddingCount)
    }
    return bytes
  }

  func remove(from bytes: Array<UInt8>, blockSize _: Int?) -> Array<UInt8> {
    for (idx, value) in bytes.reversed().enumerated() {
      if value != 0 {
        return Array(bytes[0..<bytes.count - idx])
      }
    }
    return bytes
  }
}
