/*
 * Copyright 1999-2101 Alibaba Group.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 7/11/16.
//

@import Foundation;

//! Project version number for HandyJSON.
FOUNDATION_EXPORT double HandyJSONVersionNumber;

//! Project version string for HandyJSON.
FOUNDATION_EXPORT const unsigned char HandyJSONVersionString[];

// In this header, you should import all the public headers of your framework using statements like #import <HandyJSON/PublicHeader.h>


