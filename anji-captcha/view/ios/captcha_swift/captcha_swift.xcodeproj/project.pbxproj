// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 50;
	objects = {

/* Begin PBXBuildFile section */
		D22CE74BA0A1242F38C5FEC4 /* Pods_captcha_swift.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 789D0F86A95FAC10FEF14BBE /* Pods_captcha_swift.framework */; };
		DF93C6662459390100FC2D0F /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF93C6652459390100FC2D0F /* AppDelegate.swift */; };
		DF93C6682459390100FC2D0F /* SceneDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF93C6672459390100FC2D0F /* SceneDelegate.swift */; };
		DF93C66A2459390100FC2D0F /* ViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF93C6692459390100FC2D0F /* ViewController.swift */; };
		DF93C66D2459390100FC2D0F /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = DF93C66B2459390100FC2D0F /* Main.storyboard */; };
		DF93C66F2459390200FC2D0F /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = DF93C66E2459390200FC2D0F /* Assets.xcassets */; };
		DF93C6722459390200FC2D0F /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = DF93C6702459390200FC2D0F /* LaunchScreen.storyboard */; };
		DF93C67A24596F4E00FC2D0F /* CaptchaView.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF93C67924596F4E00FC2D0F /* CaptchaView.swift */; };
		DF93C67C245986D400FC2D0F /* UIView+Extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF93C67B245986D400FC2D0F /* UIView+Extension.swift */; };
		DFA920DF24664C8300EC0383 /* CertificateTrust.swift in Sources */ = {isa = PBXBuildFile; fileRef = DFA920DE24664C8300EC0383 /* CertificateTrust.swift */; };
		DFBCF133245A709B00C6013A /* ESConfig.swift in Sources */ = {isa = PBXBuildFile; fileRef = DFBCF132245A709B00C6013A /* ESConfig.swift */; };
		DFBCF139245A751D00C6013A /* AJBaseRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = DFBCF136245A751C00C6013A /* AJBaseRequest.swift */; };
		DFBCF13C245A792400C6013A /* CaptchaRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = DFBCF13B245A792400C6013A /* CaptchaRequest.swift */; };
		DFE7B8B0246562990069F841 /* README.md in Resources */ = {isa = PBXBuildFile; fileRef = DFE7B8AF246562990069F841 /* README.md */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		789D0F86A95FAC10FEF14BBE /* Pods_captcha_swift.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_captcha_swift.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		81C8E44723D4226EFF384A3B /* Pods-captcha_swift.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-captcha_swift.release.xcconfig"; path = "Target Support Files/Pods-captcha_swift/Pods-captcha_swift.release.xcconfig"; sourceTree = "<group>"; };
		DF93C6622459390100FC2D0F /* captcha_swift.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = captcha_swift.app; sourceTree = BUILT_PRODUCTS_DIR; };
		DF93C6652459390100FC2D0F /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		DF93C6672459390100FC2D0F /* SceneDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SceneDelegate.swift; sourceTree = "<group>"; };
		DF93C6692459390100FC2D0F /* ViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ViewController.swift; sourceTree = "<group>"; };
		DF93C66C2459390100FC2D0F /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		DF93C66E2459390200FC2D0F /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		DF93C6712459390200FC2D0F /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		DF93C6732459390200FC2D0F /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		DF93C67924596F4E00FC2D0F /* CaptchaView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CaptchaView.swift; sourceTree = "<group>"; };
		DF93C67B245986D400FC2D0F /* UIView+Extension.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "UIView+Extension.swift"; sourceTree = "<group>"; };
		DFA920DE24664C8300EC0383 /* CertificateTrust.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CertificateTrust.swift; sourceTree = "<group>"; };
		DFBCF132245A709B00C6013A /* ESConfig.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ESConfig.swift; sourceTree = "<group>"; };
		DFBCF136245A751C00C6013A /* AJBaseRequest.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AJBaseRequest.swift; sourceTree = "<group>"; };
		DFBCF13B245A792400C6013A /* CaptchaRequest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CaptchaRequest.swift; sourceTree = "<group>"; };
		DFE7B8AF246562990069F841 /* README.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = README.md; sourceTree = "<group>"; };
		F6B49622B9F311C775E10C26 /* Pods-captcha_swift.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-captcha_swift.debug.xcconfig"; path = "Target Support Files/Pods-captcha_swift/Pods-captcha_swift.debug.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		DF93C65F2459390100FC2D0F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				D22CE74BA0A1242F38C5FEC4 /* Pods_captcha_swift.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		7A9740A9D9037D82D591F03B /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				789D0F86A95FAC10FEF14BBE /* Pods_captcha_swift.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		DF93C6592459390100FC2D0F = {
			isa = PBXGroup;
			children = (
				DF93C6642459390100FC2D0F /* captcha_swift */,
				DF93C6632459390100FC2D0F /* Products */,
				E5EF2B874A2D50B632045383 /* Pods */,
				7A9740A9D9037D82D591F03B /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		DF93C6632459390100FC2D0F /* Products */ = {
			isa = PBXGroup;
			children = (
				DF93C6622459390100FC2D0F /* captcha_swift.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		DF93C6642459390100FC2D0F /* captcha_swift */ = {
			isa = PBXGroup;
			children = (
				DFEA099124642B9400CB412A /* catchaview */,
				DFBCF134245A751C00C6013A /* Base */,
				DF93C6652459390100FC2D0F /* AppDelegate.swift */,
				DF93C6672459390100FC2D0F /* SceneDelegate.swift */,
				DF93C6692459390100FC2D0F /* ViewController.swift */,
				DF93C66B2459390100FC2D0F /* Main.storyboard */,
				DF93C66E2459390200FC2D0F /* Assets.xcassets */,
				DF93C6702459390200FC2D0F /* LaunchScreen.storyboard */,
				DF93C6732459390200FC2D0F /* Info.plist */,
			);
			path = captcha_swift;
			sourceTree = "<group>";
		};
		DFBCF134245A751C00C6013A /* Base */ = {
			isa = PBXGroup;
			children = (
				DFA920DE24664C8300EC0383 /* CertificateTrust.swift */,
				DFBCF132245A709B00C6013A /* ESConfig.swift */,
				DFBCF136245A751C00C6013A /* AJBaseRequest.swift */,
				DFBCF13B245A792400C6013A /* CaptchaRequest.swift */,
			);
			path = Base;
			sourceTree = "<group>";
		};
		DFEA099124642B9400CB412A /* catchaview */ = {
			isa = PBXGroup;
			children = (
				DF93C67B245986D400FC2D0F /* UIView+Extension.swift */,
				DF93C67924596F4E00FC2D0F /* CaptchaView.swift */,
				DFE7B8AF246562990069F841 /* README.md */,
			);
			path = catchaview;
			sourceTree = "<group>";
		};
		E5EF2B874A2D50B632045383 /* Pods */ = {
			isa = PBXGroup;
			children = (
				F6B49622B9F311C775E10C26 /* Pods-captcha_swift.debug.xcconfig */,
				81C8E44723D4226EFF384A3B /* Pods-captcha_swift.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		DF93C6612459390100FC2D0F /* captcha_swift */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DF93C6762459390200FC2D0F /* Build configuration list for PBXNativeTarget "captcha_swift" */;
			buildPhases = (
				30E520C2AA2E165C18D1D27C /* [CP] Check Pods Manifest.lock */,
				DF93C65E2459390100FC2D0F /* Sources */,
				DF93C65F2459390100FC2D0F /* Frameworks */,
				DF93C6602459390100FC2D0F /* Resources */,
				36938045B6E079CB4523EBDE /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = captcha_swift;
			productName = captcha_swift;
			productReference = DF93C6622459390100FC2D0F /* captcha_swift.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		DF93C65A2459390100FC2D0F /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1140;
				LastUpgradeCheck = 1140;
				ORGANIZATIONNAME = kean_qi;
				TargetAttributes = {
					DF93C6612459390100FC2D0F = {
						CreatedOnToolsVersion = 11.4.1;
					};
				};
			};
			buildConfigurationList = DF93C65D2459390100FC2D0F /* Build configuration list for PBXProject "captcha_swift" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = DF93C6592459390100FC2D0F;
			productRefGroup = DF93C6632459390100FC2D0F /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				DF93C6612459390100FC2D0F /* captcha_swift */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		DF93C6602459390100FC2D0F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DF93C6722459390200FC2D0F /* LaunchScreen.storyboard in Resources */,
				DF93C66F2459390200FC2D0F /* Assets.xcassets in Resources */,
				DFE7B8B0246562990069F841 /* README.md in Resources */,
				DF93C66D2459390100FC2D0F /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		30E520C2AA2E165C18D1D27C /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-captcha_swift-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		36938045B6E079CB4523EBDE /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-captcha_swift/Pods-captcha_swift-frameworks.sh",
				"${BUILT_PRODUCTS_DIR}/Alamofire/Alamofire.framework",
				"${BUILT_PRODUCTS_DIR}/CryptoSwift/CryptoSwift.framework",
				"${BUILT_PRODUCTS_DIR}/HandyJSON/HandyJSON.framework",
				"${BUILT_PRODUCTS_DIR}/SwiftyJSON/SwiftyJSON.framework",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
			);
			outputPaths = (
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Alamofire.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/CryptoSwift.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/HandyJSON.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/SwiftyJSON.framework",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-captcha_swift/Pods-captcha_swift-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		DF93C65E2459390100FC2D0F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DF93C66A2459390100FC2D0F /* ViewController.swift in Sources */,
				DF93C6662459390100FC2D0F /* AppDelegate.swift in Sources */,
				DF93C6682459390100FC2D0F /* SceneDelegate.swift in Sources */,
				DF93C67C245986D400FC2D0F /* UIView+Extension.swift in Sources */,
				DFBCF139245A751D00C6013A /* AJBaseRequest.swift in Sources */,
				DFA920DF24664C8300EC0383 /* CertificateTrust.swift in Sources */,
				DFBCF13C245A792400C6013A /* CaptchaRequest.swift in Sources */,
				DF93C67A24596F4E00FC2D0F /* CaptchaView.swift in Sources */,
				DFBCF133245A709B00C6013A /* ESConfig.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		DF93C66B2459390100FC2D0F /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				DF93C66C2459390100FC2D0F /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		DF93C6702459390200FC2D0F /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				DF93C6712459390200FC2D0F /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		DF93C6742459390200FC2D0F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		DF93C6752459390200FC2D0F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		DF93C6772459390200FC2D0F /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F6B49622B9F311C775E10C26 /* Pods-captcha_swift.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = 4A44TJ86VM;
				INFOPLIST_FILE = captcha_swift/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.anji.plus.captcha-swift";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		DF93C6782459390200FC2D0F /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 81C8E44723D4226EFF384A3B /* Pods-captcha_swift.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = 4A44TJ86VM;
				INFOPLIST_FILE = captcha_swift/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.anji.plus.captcha-swift";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		DF93C65D2459390100FC2D0F /* Build configuration list for PBXProject "captcha_swift" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DF93C6742459390200FC2D0F /* Debug */,
				DF93C6752459390200FC2D0F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		DF93C6762459390200FC2D0F /* Build configuration list for PBXNativeTarget "captcha_swift" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DF93C6772459390200FC2D0F /* Debug */,
				DF93C6782459390200FC2D0F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = DF93C65A2459390100FC2D0F /* Project object */;
}
