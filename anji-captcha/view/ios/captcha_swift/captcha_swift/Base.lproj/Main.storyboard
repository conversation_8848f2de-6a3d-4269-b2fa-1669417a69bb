<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="16096" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="BYZ-38-t0r">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="16087"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="Stack View standard spacing" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="tne-QT-ifu">
            <objects>
                <viewController id="BYZ-38-t0r" customClass="ViewController" customModule="captcha_swift" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="8bC-Xf-vdC">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="NIH-on-kUd">
                                <rect key="frame" x="20" y="64" width="374" height="40"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="账号" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="4Rd-iL-5iP">
                                        <rect key="frame" x="0.0" y="0.0" width="35" height="40"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" borderStyle="roundedRect" placeholder="请输入账号" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="5ml-SS-HrK">
                                        <rect key="frame" x="35" y="0.0" width="339" height="40"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                        <textInputTraits key="textInputTraits"/>
                                    </textField>
                                </subviews>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="4G8-zo-I8g"/>
                                </constraints>
                            </stackView>
                            <stackView opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="b7v-SE-mJn">
                                <rect key="frame" x="20" y="124" width="374" height="40"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="密码" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="AGD-R1-4kY">
                                        <rect key="frame" x="0.0" y="0.0" width="35" height="40"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" borderStyle="roundedRect" placeholder="请输入密码" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="dtz-sI-Rri">
                                        <rect key="frame" x="35" y="0.0" width="339" height="40"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                        <textInputTraits key="textInputTraits"/>
                                    </textField>
                                </subviews>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="t2x-q7-0xk"/>
                                </constraints>
                            </stackView>
                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillProportionally" spacingType="standard" translatesAutoresizingMaskIntoConstraints="NO" id="Fkw-fN-9Zd">
                                <rect key="frame" x="20" y="204" width="374" height="40"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="249" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="LqX-lJ-IHe">
                                        <rect key="frame" x="0.0" y="0.0" width="187" height="40"/>
                                        <color key="backgroundColor" systemColor="systemGray5Color" red="0.8980392157" green="0.8980392157" blue="0.91764705879999997" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <state key="normal" title="滑动验证码"/>
                                        <connections>
                                            <action selector="loginButtonClick:" destination="BYZ-38-t0r" eventType="touchUpInside" id="HNo-vN-mXQ"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" tag="1" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Xzc-5c-CVk">
                                        <rect key="frame" x="195" y="0.0" width="179" height="40"/>
                                        <color key="backgroundColor" systemColor="systemGray5Color" red="0.8980392157" green="0.8980392157" blue="0.91764705879999997" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <state key="normal" title="点选验证码"/>
                                        <connections>
                                            <action selector="loginButtonClick:" destination="BYZ-38-t0r" eventType="touchUpInside" id="4pQ-bS-U1a"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="DxG-f1-rHR"/>
                                </constraints>
                            </stackView>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor" cocoaTouchSystemColor="whiteColor"/>
                        <constraints>
                            <constraint firstItem="NIH-on-kUd" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" constant="20" id="WE7-oh-xDs"/>
                            <constraint firstItem="6Tk-OE-BBY" firstAttribute="trailing" secondItem="Fkw-fN-9Zd" secondAttribute="trailing" constant="20" id="aAF-EW-qik"/>
                            <constraint firstItem="Fkw-fN-9Zd" firstAttribute="top" secondItem="b7v-SE-mJn" secondAttribute="bottom" constant="40" id="aIe-KV-Wvp"/>
                            <constraint firstItem="Fkw-fN-9Zd" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" constant="20" id="baM-W1-8eW"/>
                            <constraint firstItem="b7v-SE-mJn" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" constant="20" id="bdo-g5-9HB"/>
                            <constraint firstItem="NIH-on-kUd" firstAttribute="top" secondItem="6Tk-OE-BBY" secondAttribute="top" constant="20" id="p15-cX-R7O"/>
                            <constraint firstItem="b7v-SE-mJn" firstAttribute="top" secondItem="NIH-on-kUd" secondAttribute="bottom" constant="20" id="qr7-9S-FK9"/>
                            <constraint firstItem="6Tk-OE-BBY" firstAttribute="trailing" secondItem="NIH-on-kUd" secondAttribute="trailing" constant="20" id="uLT-hU-bgD"/>
                            <constraint firstItem="6Tk-OE-BBY" firstAttribute="trailing" secondItem="b7v-SE-mJn" secondAttribute="trailing" constant="20" id="xy3-4R-Dda"/>
                        </constraints>
                        <viewLayoutGuide key="safeArea" id="6Tk-OE-BBY"/>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="dkx-z0-nzr" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="137.68115942028987" y="118.52678571428571"/>
        </scene>
    </scenes>
</document>
