// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 50;
	objects = {

/* Begin PBXBuildFile section */
		01BEB06B4E262DCEA7A94F4206311BF9 /* UIRefreshControl+AFNetworking.m in Sources */ = {isa = PBXBuildFile; fileRef = FE924FCF3297AB8F4131AC11BC0251AE /* UIRefreshControl+AFNetworking.m */; };
		02937E01710489D6668D0C3700E8EADA /* UIProgressView+AFNetworking.m in Sources */ = {isa = PBXBuildFile; fileRef = F51B7DF99A2A7D1F069B1136E8837A28 /* UIProgressView+AFNetworking.m */; };
		02E2954BD351E7B167D8F60B13208DAF /* JSONModelError.h in Headers */ = {isa = PBXBuildFile; fileRef = 7E03C0D70592B5ACDB865195284CDBDB /* JSONModelError.h */; settings = {ATTRIBUTES = (Public, ); }; };
		07E08C56386C02093F27CF28E4D01F11 /* WKWebView+AFNetworking.h in Headers */ = {isa = PBXBuildFile; fileRef = 8BE20BF12CC78F8F55F45D8C1B067E44 /* WKWebView+AFNetworking.h */; settings = {ATTRIBUTES = (Public, ); }; };
		08D5C0606D6FBFB4877FC3C3FF4A2208 /* AFNetworking-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = B7B44E0F25E761494CD1A925B1E772FF /* AFNetworking-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		1071D84B6431759A11FAEBAAE29E3F32 /* JSONHTTPClient.h in Headers */ = {isa = PBXBuildFile; fileRef = 780FA5E1D1D03B0AC2F0D90A4A5634A8 /* JSONHTTPClient.h */; settings = {ATTRIBUTES = (Public, ); }; };
		110BDB27F35AB2C27F0B19E2E3B30B77 /* Pods-captcha_oc-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 0EC0EED636630BD419B89187ED0FC95D /* Pods-captcha_oc-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		163B07BE98EAA270AF0A7AA82F84208E /* AFNetworkReachabilityManager.h in Headers */ = {isa = PBXBuildFile; fileRef = C06BD97828CEF1C6D00F315A0941FE12 /* AFNetworkReachabilityManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		191126E8D5A1E26339D2B767EF3AE197 /* JSONModel+networking.h in Headers */ = {isa = PBXBuildFile; fileRef = 046A62C3234F067A0D32B99A7D7DC9E1 /* JSONModel+networking.h */; settings = {ATTRIBUTES = (Public, ); }; };
		232B2E8F32708EF2D85000B997FB69A2 /* UIImageView+AFNetworking.m in Sources */ = {isa = PBXBuildFile; fileRef = 7F395AA98529E36C8D4174333A163E6E /* UIImageView+AFNetworking.m */; };
		2657AB4F1D04803DABFE7DDF80C0EF7F /* AFURLRequestSerialization.h in Headers */ = {isa = PBXBuildFile; fileRef = 99B400381F3ED939EC7E7433E45DC307 /* AFURLRequestSerialization.h */; settings = {ATTRIBUTES = (Public, ); }; };
		26A3D3037A05E7E99AFF098D11427775 /* JSONKeyMapper.m in Sources */ = {isa = PBXBuildFile; fileRef = EB4BCC19A4BBA27CC8380DF9C9DFFD05 /* JSONKeyMapper.m */; };
		27256E913790A1EC111854E3D10E67F5 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 3212113385A8FBBDB272BD23C409FF61 /* Foundation.framework */; };
		2AB5C7D99BD529942B2C81AB0CD8A600 /* UIRefreshControl+AFNetworking.h in Headers */ = {isa = PBXBuildFile; fileRef = 82C1797D34FE2B9461D6169ED5510B46 /* UIRefreshControl+AFNetworking.h */; settings = {ATTRIBUTES = (Public, ); }; };
		2E7446308877A2FA6F48A7E545FFB1B1 /* AFNetworkReachabilityManager.m in Sources */ = {isa = PBXBuildFile; fileRef = C0BD9E8EDBE8E4A40A96C1DEA705FBF4 /* AFNetworkReachabilityManager.m */; };
		30DF77E53D2E837133265614EC8BEDA8 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 3212113385A8FBBDB272BD23C409FF61 /* Foundation.framework */; };
		38B57953D8705FA2092852071CD3A462 /* UIButton+AFNetworking.m in Sources */ = {isa = PBXBuildFile; fileRef = 89949E535DDCFD1B2E3F348C5E66D5D0 /* UIButton+AFNetworking.m */; };
		3AEA2F22B88CD78E671F25B60DB66ADA /* JSONAPI.h in Headers */ = {isa = PBXBuildFile; fileRef = 6ABAED90B57BE5734C65EAFC47CFCAAF /* JSONAPI.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3CA0C9185ED4469D427AA53B26E1B18F /* UIProgressView+AFNetworking.h in Headers */ = {isa = PBXBuildFile; fileRef = 2E3E82FE5D990551C65481EC8BFECED7 /* UIProgressView+AFNetworking.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3EB561D79F76C06AA538872D6302D35D /* AFNetworking-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 11A0FEBED6035BF96EA9D80CF1D9D9EB /* AFNetworking-dummy.m */; };
		40426F061CD9B0B435C690CAFEA2BD00 /* UIButton+AFNetworking.h in Headers */ = {isa = PBXBuildFile; fileRef = 66496F623929C7C2499D28145BD854DF /* UIButton+AFNetworking.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4908757791D70EC3E10CFD5D74A3D7DB /* Pods-captcha_oc-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = D9D366FB0585701BCD4DB7CC8D33D400 /* Pods-captcha_oc-dummy.m */; };
		4992259A954F174FA8BDB394DB1C8F93 /* AFAutoPurgingImageCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 54826E2EA4F71B12A0BA5E56203F7E45 /* AFAutoPurgingImageCache.m */; };
		4FCA4E8703DBF3D3FB7D5BA06229159D /* UIKit+AFNetworking.h in Headers */ = {isa = PBXBuildFile; fileRef = 353F0AB4A677201BC1E7513CA31C3973 /* UIKit+AFNetworking.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6EE482EF823C5090378A600998A120F9 /* AFAutoPurgingImageCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 22C6F06A5B0CD67E1079011EFECB28F0 /* AFAutoPurgingImageCache.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6FCDE0D44543F4DABF90A88E056D8199 /* JSONModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 73CAABE3ECA603E6CA212B46479C15D8 /* JSONModel.m */; };
		722EC0191A173ECB2121618467D32DF4 /* AFNetworkActivityIndicatorManager.h in Headers */ = {isa = PBXBuildFile; fileRef = B91B39DC6875B3335CB73C86659EB6EF /* AFNetworkActivityIndicatorManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		75C939C90205A780AB001C84A0B90C24 /* JSONModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 2E9F32A6F0CAB55F47F59E2FB6C85ADF /* JSONModel.h */; settings = {ATTRIBUTES = (Public, ); }; };
		76536A816A576F08FADD3366A2E40094 /* AFURLSessionManager.m in Sources */ = {isa = PBXBuildFile; fileRef = A2C4AACC2EAF22AC21EEF34B9507B083 /* AFURLSessionManager.m */; };
		8082228023D40A10FDED596334868F3B /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 3212113385A8FBBDB272BD23C409FF61 /* Foundation.framework */; };
		828919193B61FC103B8846F54EF3131C /* AFURLResponseSerialization.m in Sources */ = {isa = PBXBuildFile; fileRef = D1D5AABD355B0DD1C49D1335D9D55341 /* AFURLResponseSerialization.m */; };
		8CE480485EFF2F15B7A394C90CFF274A /* AFHTTPSessionManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 6B9B7FAB8B749994510263464755D7F5 /* AFHTTPSessionManager.m */; };
		8D060BB549FA8C46C8A5E230C7E00CFE /* AFURLResponseSerialization.h in Headers */ = {isa = PBXBuildFile; fileRef = 5BBBBFB84AB4FD6A1944FE71B5FFC7E0 /* AFURLResponseSerialization.h */; settings = {ATTRIBUTES = (Public, ); }; };
		951B6669114E3C8D2E4961AFE2BB6647 /* WKWebView+AFNetworking.m in Sources */ = {isa = PBXBuildFile; fileRef = 2764AA8DABF8D76054F488460CD8F656 /* WKWebView+AFNetworking.m */; };
		9D3A67B86C6A3A1339783953B3E833B9 /* AFNetworkActivityIndicatorManager.m in Sources */ = {isa = PBXBuildFile; fileRef = D4E8EDE3FD818BE08A2F6E2E569F91FE /* AFNetworkActivityIndicatorManager.m */; };
		A52DD788A5384665F425D6A2C7493AD0 /* JSONModelError.m in Sources */ = {isa = PBXBuildFile; fileRef = 1EEED4A6D438BB482C9F5CA8E7E0BA30 /* JSONModelError.m */; };
		A6DE5A1B478949FE0444318D47C1A9CC /* AFSecurityPolicy.m in Sources */ = {isa = PBXBuildFile; fileRef = 9D1624E942265C016F7E082E47ECCBC8 /* AFSecurityPolicy.m */; };
		A7E6C236F5C97B35484B553454B609B0 /* AFURLSessionManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 8B2161DCD65B94F628389FC1CC116444 /* AFURLSessionManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AECFCA2598AABAEB044F6C4594F3222F /* AFSecurityPolicy.h in Headers */ = {isa = PBXBuildFile; fileRef = BE468D7759C7334EE74023716FD28832 /* AFSecurityPolicy.h */; settings = {ATTRIBUTES = (Public, ); }; };
		B0BA1C31A2A3AB25BC95A4B2DE0A7B5D /* JSONValueTransformer.m in Sources */ = {isa = PBXBuildFile; fileRef = 2213AD56B78101D12DABA8C37609945A /* JSONValueTransformer.m */; };
		B4658F8495B18BD4EB5D893CAFF3BBBC /* AFImageDownloader.h in Headers */ = {isa = PBXBuildFile; fileRef = 39077A07F373311612D0FFF4C2942CCA /* AFImageDownloader.h */; settings = {ATTRIBUTES = (Public, ); }; };
		B4C7BC969594EB3E9323674B7FF7C66E /* JSONHTTPClient.m in Sources */ = {isa = PBXBuildFile; fileRef = B95EFC8A9C4290AC8A87A7764EA7EB5A /* JSONHTTPClient.m */; };
		B83B1CA17424975882DA1066536E112B /* AFImageDownloader.m in Sources */ = {isa = PBXBuildFile; fileRef = 069D84A08E0285253361F240A042F870 /* AFImageDownloader.m */; };
		BBFB85B525E1E77FCB19C84D911205D8 /* AFURLRequestSerialization.m in Sources */ = {isa = PBXBuildFile; fileRef = DA5965686232FFA5AE624DD0C3E09049 /* AFURLRequestSerialization.m */; };
		C25BD96E691172D62B98B2F5F8D7F0C7 /* JSONModelLib.h in Headers */ = {isa = PBXBuildFile; fileRef = 54E4FF1014A73E55E0A06608F8769D34 /* JSONModelLib.h */; settings = {ATTRIBUTES = (Public, ); }; };
		C64C8DE746D376EEA160794275302677 /* JSONModelClassProperty.m in Sources */ = {isa = PBXBuildFile; fileRef = 2651CD7EABFF34529D1FC61ECC671988 /* JSONModelClassProperty.m */; };
		C65CBBF120A2DD097498BFA38119D466 /* JSONAPI.m in Sources */ = {isa = PBXBuildFile; fileRef = 5EDDA4D6895058F3A3FDCEC51B09C198 /* JSONAPI.m */; };
		C7850AB3F7B5FDCA4F1F47F1EC205276 /* JSONKeyMapper.h in Headers */ = {isa = PBXBuildFile; fileRef = 0BB58A46BF811F5896E43608BBAC959F /* JSONKeyMapper.h */; settings = {ATTRIBUTES = (Public, ); }; };
		C9F701A2E6208D9E840E7A91287F21B9 /* AFHTTPSessionManager.h in Headers */ = {isa = PBXBuildFile; fileRef = AB91561D44E0BB5BD8C81C06A3575EBD /* AFHTTPSessionManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		CFD38BDDB48F8D35B9A858E957A933A2 /* JSONModel+networking.m in Sources */ = {isa = PBXBuildFile; fileRef = 6BBC7E8C5267276C051FFA82E6783134 /* JSONModel+networking.m */; };
		D358BCEB69D2E10B9BC6504C93D7D14C /* JSONModelClassProperty.h in Headers */ = {isa = PBXBuildFile; fileRef = 3C80FB8AE7AB5AC1A8AEBA5AAAD2FADE /* JSONModelClassProperty.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D66716504D475B6377F7DD5C7C2BBCDE /* AFNetworking.h in Headers */ = {isa = PBXBuildFile; fileRef = 9657C0E560736F937697C3F260ECA79A /* AFNetworking.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DFF8D80125C062F6646E6EE798E6ABA3 /* JSONModel-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 5511D0D905FBF511A66F8AD39CC5D747 /* JSONModel-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		E3886BA871A93B03E984D8BD7823D947 /* JSONValueTransformer.h in Headers */ = {isa = PBXBuildFile; fileRef = AD10C0507E5C1ADFFCAC104D4AE24F37 /* JSONValueTransformer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		E8EB9F2CFC54AFE86B618DB528647311 /* UIActivityIndicatorView+AFNetworking.h in Headers */ = {isa = PBXBuildFile; fileRef = 36EC8F11ACDEABCEB2222AEB3A491C9C /* UIActivityIndicatorView+AFNetworking.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F191431A32F803DC12F12EF3B4D984BD /* UIImageView+AFNetworking.h in Headers */ = {isa = PBXBuildFile; fileRef = EFDBE32B4F632386ADCFB7C67CE3127C /* UIImageView+AFNetworking.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F4352388490E70C1CF187D93E390FB2A /* AFCompatibilityMacros.h in Headers */ = {isa = PBXBuildFile; fileRef = 9610DE8C1A36299E2DFA9D7F51695EE6 /* AFCompatibilityMacros.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F442593E37B8F49398FDB1974EDF3FD6 /* JSONModel-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 81B734E13D8048FBD7ED14D02AFDC1F4 /* JSONModel-dummy.m */; };
		FDA522B328C907FBF9B11C64C1A7C624 /* UIActivityIndicatorView+AFNetworking.m in Sources */ = {isa = PBXBuildFile; fileRef = 881C9D882BFBDE5A0AE428735341A10D /* UIActivityIndicatorView+AFNetworking.m */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		A7EE010AC3B25F0F77487482301479C4 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = E0EAF4371C6E4B30A1779D513CB4355B;
			remoteInfo = JSONModel;
		};
		B676510069A0115B0757504FE153B6AD /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 0130B3724283586C0E9D2A112D4F2AA1;
			remoteInfo = AFNetworking;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		046A62C3234F067A0D32B99A7D7DC9E1 /* JSONModel+networking.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "JSONModel+networking.h"; path = "JSONModel/JSONModelNetworking/JSONModel+networking.h"; sourceTree = "<group>"; };
		069D84A08E0285253361F240A042F870 /* AFImageDownloader.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AFImageDownloader.m; path = "UIKit+AFNetworking/AFImageDownloader.m"; sourceTree = "<group>"; };
		0BB58A46BF811F5896E43608BBAC959F /* JSONKeyMapper.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = JSONKeyMapper.h; path = JSONModel/JSONModelTransformations/JSONKeyMapper.h; sourceTree = "<group>"; };
		0BF6959B3F4E9AB00FA97EFFF0F6837C /* AFNetworking.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = AFNetworking.modulemap; sourceTree = "<group>"; };
		0EC0EED636630BD419B89187ED0FC95D /* Pods-captcha_oc-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-captcha_oc-umbrella.h"; sourceTree = "<group>"; };
		11A0FEBED6035BF96EA9D80CF1D9D9EB /* AFNetworking-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "AFNetworking-dummy.m"; sourceTree = "<group>"; };
		1EEED4A6D438BB482C9F5CA8E7E0BA30 /* JSONModelError.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = JSONModelError.m; path = JSONModel/JSONModel/JSONModelError.m; sourceTree = "<group>"; };
		2213AD56B78101D12DABA8C37609945A /* JSONValueTransformer.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = JSONValueTransformer.m; path = JSONModel/JSONModelTransformations/JSONValueTransformer.m; sourceTree = "<group>"; };
		22C6F06A5B0CD67E1079011EFECB28F0 /* AFAutoPurgingImageCache.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AFAutoPurgingImageCache.h; path = "UIKit+AFNetworking/AFAutoPurgingImageCache.h"; sourceTree = "<group>"; };
		23B1B9E4464A77FA740351C0113202A1 /* Pods-captcha_oc.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-captcha_oc.release.xcconfig"; sourceTree = "<group>"; };
		2651CD7EABFF34529D1FC61ECC671988 /* JSONModelClassProperty.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = JSONModelClassProperty.m; path = JSONModel/JSONModel/JSONModelClassProperty.m; sourceTree = "<group>"; };
		2764AA8DABF8D76054F488460CD8F656 /* WKWebView+AFNetworking.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "WKWebView+AFNetworking.m"; path = "UIKit+AFNetworking/WKWebView+AFNetworking.m"; sourceTree = "<group>"; };
		2E3E82FE5D990551C65481EC8BFECED7 /* UIProgressView+AFNetworking.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIProgressView+AFNetworking.h"; path = "UIKit+AFNetworking/UIProgressView+AFNetworking.h"; sourceTree = "<group>"; };
		2E9F32A6F0CAB55F47F59E2FB6C85ADF /* JSONModel.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = JSONModel.h; path = JSONModel/JSONModel/JSONModel.h; sourceTree = "<group>"; };
		3212113385A8FBBDB272BD23C409FF61 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS12.2.sdk/System/Library/Frameworks/Foundation.framework; sourceTree = DEVELOPER_DIR; };
		353F0AB4A677201BC1E7513CA31C3973 /* UIKit+AFNetworking.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIKit+AFNetworking.h"; path = "UIKit+AFNetworking/UIKit+AFNetworking.h"; sourceTree = "<group>"; };
		36EC8F11ACDEABCEB2222AEB3A491C9C /* UIActivityIndicatorView+AFNetworking.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIActivityIndicatorView+AFNetworking.h"; path = "UIKit+AFNetworking/UIActivityIndicatorView+AFNetworking.h"; sourceTree = "<group>"; };
		39077A07F373311612D0FFF4C2942CCA /* AFImageDownloader.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AFImageDownloader.h; path = "UIKit+AFNetworking/AFImageDownloader.h"; sourceTree = "<group>"; };
		395B8C7688FE05E151B7E8877157D627 /* AFNetworking.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = AFNetworking.release.xcconfig; sourceTree = "<group>"; };
		3C80FB8AE7AB5AC1A8AEBA5AAAD2FADE /* JSONModelClassProperty.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = JSONModelClassProperty.h; path = JSONModel/JSONModel/JSONModelClassProperty.h; sourceTree = "<group>"; };
		444541C8D6027EAB54536560D35EE988 /* Pods-captcha_oc.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-captcha_oc.debug.xcconfig"; sourceTree = "<group>"; };
		54826E2EA4F71B12A0BA5E56203F7E45 /* AFAutoPurgingImageCache.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AFAutoPurgingImageCache.m; path = "UIKit+AFNetworking/AFAutoPurgingImageCache.m"; sourceTree = "<group>"; };
		54E4FF1014A73E55E0A06608F8769D34 /* JSONModelLib.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = JSONModelLib.h; path = JSONModel/JSONModelLib.h; sourceTree = "<group>"; };
		5511D0D905FBF511A66F8AD39CC5D747 /* JSONModel-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "JSONModel-umbrella.h"; sourceTree = "<group>"; };
		597FA810FADAB98C80215DA5EE4DAA01 /* Pods-captcha_oc-frameworks.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "Pods-captcha_oc-frameworks.sh"; sourceTree = "<group>"; };
		5BBBBFB84AB4FD6A1944FE71B5FFC7E0 /* AFURLResponseSerialization.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AFURLResponseSerialization.h; path = AFNetworking/AFURLResponseSerialization.h; sourceTree = "<group>"; };
		5EDDA4D6895058F3A3FDCEC51B09C198 /* JSONAPI.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = JSONAPI.m; path = JSONModel/JSONModelNetworking/JSONAPI.m; sourceTree = "<group>"; };
		66496F623929C7C2499D28145BD854DF /* UIButton+AFNetworking.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIButton+AFNetworking.h"; path = "UIKit+AFNetworking/UIButton+AFNetworking.h"; sourceTree = "<group>"; };
		6ABAED90B57BE5734C65EAFC47CFCAAF /* JSONAPI.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = JSONAPI.h; path = JSONModel/JSONModelNetworking/JSONAPI.h; sourceTree = "<group>"; };
		6B6ED7032886979E7B6B189EFF3BADC6 /* Pods-captcha_oc-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-captcha_oc-acknowledgements.plist"; sourceTree = "<group>"; };
		6B9B7FAB8B749994510263464755D7F5 /* AFHTTPSessionManager.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AFHTTPSessionManager.m; path = AFNetworking/AFHTTPSessionManager.m; sourceTree = "<group>"; };
		6BBC7E8C5267276C051FFA82E6783134 /* JSONModel+networking.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "JSONModel+networking.m"; path = "JSONModel/JSONModelNetworking/JSONModel+networking.m"; sourceTree = "<group>"; };
		73CAABE3ECA603E6CA212B46479C15D8 /* JSONModel.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = JSONModel.m; path = JSONModel/JSONModel/JSONModel.m; sourceTree = "<group>"; };
		780FA5E1D1D03B0AC2F0D90A4A5634A8 /* JSONHTTPClient.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = JSONHTTPClient.h; path = JSONModel/JSONModelNetworking/JSONHTTPClient.h; sourceTree = "<group>"; };
		7E03C0D70592B5ACDB865195284CDBDB /* JSONModelError.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = JSONModelError.h; path = JSONModel/JSONModel/JSONModelError.h; sourceTree = "<group>"; };
		7F395AA98529E36C8D4174333A163E6E /* UIImageView+AFNetworking.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIImageView+AFNetworking.m"; path = "UIKit+AFNetworking/UIImageView+AFNetworking.m"; sourceTree = "<group>"; };
		81B734E13D8048FBD7ED14D02AFDC1F4 /* JSONModel-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "JSONModel-dummy.m"; sourceTree = "<group>"; };
		82C1797D34FE2B9461D6169ED5510B46 /* UIRefreshControl+AFNetworking.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIRefreshControl+AFNetworking.h"; path = "UIKit+AFNetworking/UIRefreshControl+AFNetworking.h"; sourceTree = "<group>"; };
		86DFE82DC0E3C6E4910803C4A60F651B /* JSONModel-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "JSONModel-Info.plist"; sourceTree = "<group>"; };
		881C9D882BFBDE5A0AE428735341A10D /* UIActivityIndicatorView+AFNetworking.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIActivityIndicatorView+AFNetworking.m"; path = "UIKit+AFNetworking/UIActivityIndicatorView+AFNetworking.m"; sourceTree = "<group>"; };
		89949E535DDCFD1B2E3F348C5E66D5D0 /* UIButton+AFNetworking.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIButton+AFNetworking.m"; path = "UIKit+AFNetworking/UIButton+AFNetworking.m"; sourceTree = "<group>"; };
		8B2161DCD65B94F628389FC1CC116444 /* AFURLSessionManager.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AFURLSessionManager.h; path = AFNetworking/AFURLSessionManager.h; sourceTree = "<group>"; };
		8BE20BF12CC78F8F55F45D8C1B067E44 /* WKWebView+AFNetworking.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "WKWebView+AFNetworking.h"; path = "UIKit+AFNetworking/WKWebView+AFNetworking.h"; sourceTree = "<group>"; };
		8C3601E58CF6D7410F2A3E516563315D /* AFNetworking-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "AFNetworking-prefix.pch"; sourceTree = "<group>"; };
		9610DE8C1A36299E2DFA9D7F51695EE6 /* AFCompatibilityMacros.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AFCompatibilityMacros.h; path = AFNetworking/AFCompatibilityMacros.h; sourceTree = "<group>"; };
		9612529221593795D50BAA5CBDBD629B /* JSONModel-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "JSONModel-prefix.pch"; sourceTree = "<group>"; };
		9657C0E560736F937697C3F260ECA79A /* AFNetworking.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AFNetworking.h; path = AFNetworking/AFNetworking.h; sourceTree = "<group>"; };
		99B400381F3ED939EC7E7433E45DC307 /* AFURLRequestSerialization.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AFURLRequestSerialization.h; path = AFNetworking/AFURLRequestSerialization.h; sourceTree = "<group>"; };
		99F2EB107B1682A61E3A3B9153F28788 /* JSONModel.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = JSONModel.release.xcconfig; sourceTree = "<group>"; };
		9D1624E942265C016F7E082E47ECCBC8 /* AFSecurityPolicy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AFSecurityPolicy.m; path = AFNetworking/AFSecurityPolicy.m; sourceTree = "<group>"; };
		9D940727FF8FB9C785EB98E56350EF41 /* Podfile */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = Podfile; path = ../Podfile; sourceTree = SOURCE_ROOT; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		A2C4AACC2EAF22AC21EEF34B9507B083 /* AFURLSessionManager.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AFURLSessionManager.m; path = AFNetworking/AFURLSessionManager.m; sourceTree = "<group>"; };
		A32F6EC47D990CEB2E6F525B3485B68C /* Pods-captcha_oc.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-captcha_oc.modulemap"; sourceTree = "<group>"; };
		A4FA15D44DF6BAC7550EDEED10862AA3 /* AFNetworking.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = AFNetworking.framework; path = AFNetworking.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		AB91561D44E0BB5BD8C81C06A3575EBD /* AFHTTPSessionManager.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AFHTTPSessionManager.h; path = AFNetworking/AFHTTPSessionManager.h; sourceTree = "<group>"; };
		AD10C0507E5C1ADFFCAC104D4AE24F37 /* JSONValueTransformer.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = JSONValueTransformer.h; path = JSONModel/JSONModelTransformations/JSONValueTransformer.h; sourceTree = "<group>"; };
		B0922C0F772C2170B0D47D284E4B813B /* Pods-captcha_oc-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-captcha_oc-acknowledgements.markdown"; sourceTree = "<group>"; };
		B7B44E0F25E761494CD1A925B1E772FF /* AFNetworking-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "AFNetworking-umbrella.h"; sourceTree = "<group>"; };
		B91B39DC6875B3335CB73C86659EB6EF /* AFNetworkActivityIndicatorManager.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AFNetworkActivityIndicatorManager.h; path = "UIKit+AFNetworking/AFNetworkActivityIndicatorManager.h"; sourceTree = "<group>"; };
		B95EFC8A9C4290AC8A87A7764EA7EB5A /* JSONHTTPClient.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = JSONHTTPClient.m; path = JSONModel/JSONModelNetworking/JSONHTTPClient.m; sourceTree = "<group>"; };
		BAACA1BF7AB3DD28402C4F9A7C2FD104 /* JSONModel.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = JSONModel.framework; path = JSONModel.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		BE468D7759C7334EE74023716FD28832 /* AFSecurityPolicy.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AFSecurityPolicy.h; path = AFNetworking/AFSecurityPolicy.h; sourceTree = "<group>"; };
		C06BD97828CEF1C6D00F315A0941FE12 /* AFNetworkReachabilityManager.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = AFNetworkReachabilityManager.h; path = AFNetworking/AFNetworkReachabilityManager.h; sourceTree = "<group>"; };
		C0BD9E8EDBE8E4A40A96C1DEA705FBF4 /* AFNetworkReachabilityManager.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AFNetworkReachabilityManager.m; path = AFNetworking/AFNetworkReachabilityManager.m; sourceTree = "<group>"; };
		C2067E7FE3B5921D6A863E86D53F9418 /* JSONModel.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = JSONModel.modulemap; sourceTree = "<group>"; };
		CEC9277B64020F21644359C5EEF15D00 /* Pods_captcha_oc.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = Pods_captcha_oc.framework; path = "Pods-captcha_oc.framework"; sourceTree = BUILT_PRODUCTS_DIR; };
		D1D5AABD355B0DD1C49D1335D9D55341 /* AFURLResponseSerialization.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AFURLResponseSerialization.m; path = AFNetworking/AFURLResponseSerialization.m; sourceTree = "<group>"; };
		D4E8EDE3FD818BE08A2F6E2E569F91FE /* AFNetworkActivityIndicatorManager.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AFNetworkActivityIndicatorManager.m; path = "UIKit+AFNetworking/AFNetworkActivityIndicatorManager.m"; sourceTree = "<group>"; };
		D9D366FB0585701BCD4DB7CC8D33D400 /* Pods-captcha_oc-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-captcha_oc-dummy.m"; sourceTree = "<group>"; };
		DA5965686232FFA5AE624DD0C3E09049 /* AFURLRequestSerialization.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AFURLRequestSerialization.m; path = AFNetworking/AFURLRequestSerialization.m; sourceTree = "<group>"; };
		DD8F57CF5419E39D25C5CE73703723AF /* AFNetworking-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "AFNetworking-Info.plist"; sourceTree = "<group>"; };
		E33ED7ECA7EDB642F3677AF95B0B0321 /* JSONModel.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = JSONModel.debug.xcconfig; sourceTree = "<group>"; };
		EB4BCC19A4BBA27CC8380DF9C9DFFD05 /* JSONKeyMapper.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = JSONKeyMapper.m; path = JSONModel/JSONModelTransformations/JSONKeyMapper.m; sourceTree = "<group>"; };
		EFDBE32B4F632386ADCFB7C67CE3127C /* UIImageView+AFNetworking.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "UIImageView+AFNetworking.h"; path = "UIKit+AFNetworking/UIImageView+AFNetworking.h"; sourceTree = "<group>"; };
		F51B7DF99A2A7D1F069B1136E8837A28 /* UIProgressView+AFNetworking.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIProgressView+AFNetworking.m"; path = "UIKit+AFNetworking/UIProgressView+AFNetworking.m"; sourceTree = "<group>"; };
		F600698801612B6FC6389DE2801F6ECD /* AFNetworking.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = AFNetworking.debug.xcconfig; sourceTree = "<group>"; };
		FDB75B15125EF90464A73598271942C3 /* Pods-captcha_oc-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-captcha_oc-Info.plist"; sourceTree = "<group>"; };
		FE924FCF3297AB8F4131AC11BC0251AE /* UIRefreshControl+AFNetworking.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIRefreshControl+AFNetworking.m"; path = "UIKit+AFNetworking/UIRefreshControl+AFNetworking.m"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		886E050E05F79FD43F2409BED7E67F73 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				30DF77E53D2E837133265614EC8BEDA8 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8F9BCCCF510F65077210B8D9944565EC /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				27256E913790A1EC111854E3D10E67F5 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E1547060E5BB5BC0AA9A9658E356C04B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8082228023D40A10FDED596334868F3B /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		0E35011DC8DF48A5ABC9C82760C53040 /* Pods-captcha_oc */ = {
			isa = PBXGroup;
			children = (
				A32F6EC47D990CEB2E6F525B3485B68C /* Pods-captcha_oc.modulemap */,
				B0922C0F772C2170B0D47D284E4B813B /* Pods-captcha_oc-acknowledgements.markdown */,
				6B6ED7032886979E7B6B189EFF3BADC6 /* Pods-captcha_oc-acknowledgements.plist */,
				D9D366FB0585701BCD4DB7CC8D33D400 /* Pods-captcha_oc-dummy.m */,
				597FA810FADAB98C80215DA5EE4DAA01 /* Pods-captcha_oc-frameworks.sh */,
				FDB75B15125EF90464A73598271942C3 /* Pods-captcha_oc-Info.plist */,
				0EC0EED636630BD419B89187ED0FC95D /* Pods-captcha_oc-umbrella.h */,
				444541C8D6027EAB54536560D35EE988 /* Pods-captcha_oc.debug.xcconfig */,
				23B1B9E4464A77FA740351C0113202A1 /* Pods-captcha_oc.release.xcconfig */,
			);
			name = "Pods-captcha_oc";
			path = "Target Support Files/Pods-captcha_oc";
			sourceTree = "<group>";
		};
		3948091CAA9B4038F44F7EB7C72262E1 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				C2067E7FE3B5921D6A863E86D53F9418 /* JSONModel.modulemap */,
				81B734E13D8048FBD7ED14D02AFDC1F4 /* JSONModel-dummy.m */,
				86DFE82DC0E3C6E4910803C4A60F651B /* JSONModel-Info.plist */,
				9612529221593795D50BAA5CBDBD629B /* JSONModel-prefix.pch */,
				5511D0D905FBF511A66F8AD39CC5D747 /* JSONModel-umbrella.h */,
				E33ED7ECA7EDB642F3677AF95B0B0321 /* JSONModel.debug.xcconfig */,
				99F2EB107B1682A61E3A3B9153F28788 /* JSONModel.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/JSONModel";
			sourceTree = "<group>";
		};
		3C126D8664812822351E5813A7D90761 /* Targets Support Files */ = {
			isa = PBXGroup;
			children = (
				0E35011DC8DF48A5ABC9C82760C53040 /* Pods-captcha_oc */,
			);
			name = "Targets Support Files";
			sourceTree = "<group>";
		};
		518CE419F21E21E96FC6A42B44FFEA6C /* JSONModel */ = {
			isa = PBXGroup;
			children = (
				6ABAED90B57BE5734C65EAFC47CFCAAF /* JSONAPI.h */,
				5EDDA4D6895058F3A3FDCEC51B09C198 /* JSONAPI.m */,
				780FA5E1D1D03B0AC2F0D90A4A5634A8 /* JSONHTTPClient.h */,
				B95EFC8A9C4290AC8A87A7764EA7EB5A /* JSONHTTPClient.m */,
				0BB58A46BF811F5896E43608BBAC959F /* JSONKeyMapper.h */,
				EB4BCC19A4BBA27CC8380DF9C9DFFD05 /* JSONKeyMapper.m */,
				2E9F32A6F0CAB55F47F59E2FB6C85ADF /* JSONModel.h */,
				73CAABE3ECA603E6CA212B46479C15D8 /* JSONModel.m */,
				046A62C3234F067A0D32B99A7D7DC9E1 /* JSONModel+networking.h */,
				6BBC7E8C5267276C051FFA82E6783134 /* JSONModel+networking.m */,
				3C80FB8AE7AB5AC1A8AEBA5AAAD2FADE /* JSONModelClassProperty.h */,
				2651CD7EABFF34529D1FC61ECC671988 /* JSONModelClassProperty.m */,
				7E03C0D70592B5ACDB865195284CDBDB /* JSONModelError.h */,
				1EEED4A6D438BB482C9F5CA8E7E0BA30 /* JSONModelError.m */,
				54E4FF1014A73E55E0A06608F8769D34 /* JSONModelLib.h */,
				AD10C0507E5C1ADFFCAC104D4AE24F37 /* JSONValueTransformer.h */,
				2213AD56B78101D12DABA8C37609945A /* JSONValueTransformer.m */,
				3948091CAA9B4038F44F7EB7C72262E1 /* Support Files */,
			);
			name = JSONModel;
			path = JSONModel;
			sourceTree = "<group>";
		};
		537D48154C5C5047DF92C5345CAEB149 /* Reachability */ = {
			isa = PBXGroup;
			children = (
				C06BD97828CEF1C6D00F315A0941FE12 /* AFNetworkReachabilityManager.h */,
				C0BD9E8EDBE8E4A40A96C1DEA705FBF4 /* AFNetworkReachabilityManager.m */,
			);
			name = Reachability;
			sourceTree = "<group>";
		};
		5776F2121431D14D43B46EC8E5298DAB /* Products */ = {
			isa = PBXGroup;
			children = (
				A4FA15D44DF6BAC7550EDEED10862AA3 /* AFNetworking.framework */,
				BAACA1BF7AB3DD28402C4F9A7C2FD104 /* JSONModel.framework */,
				CEC9277B64020F21644359C5EEF15D00 /* Pods_captcha_oc.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		5A38CA38A681531682D8F4F2BE03C2C3 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				0BF6959B3F4E9AB00FA97EFFF0F6837C /* AFNetworking.modulemap */,
				11A0FEBED6035BF96EA9D80CF1D9D9EB /* AFNetworking-dummy.m */,
				DD8F57CF5419E39D25C5CE73703723AF /* AFNetworking-Info.plist */,
				8C3601E58CF6D7410F2A3E516563315D /* AFNetworking-prefix.pch */,
				B7B44E0F25E761494CD1A925B1E772FF /* AFNetworking-umbrella.h */,
				F600698801612B6FC6389DE2801F6ECD /* AFNetworking.debug.xcconfig */,
				395B8C7688FE05E151B7E8877157D627 /* AFNetworking.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/AFNetworking";
			sourceTree = "<group>";
		};
		5B9AEBCF98B6A6BC086B6A965CEE097C /* UIKit */ = {
			isa = PBXGroup;
			children = (
				22C6F06A5B0CD67E1079011EFECB28F0 /* AFAutoPurgingImageCache.h */,
				54826E2EA4F71B12A0BA5E56203F7E45 /* AFAutoPurgingImageCache.m */,
				39077A07F373311612D0FFF4C2942CCA /* AFImageDownloader.h */,
				069D84A08E0285253361F240A042F870 /* AFImageDownloader.m */,
				B91B39DC6875B3335CB73C86659EB6EF /* AFNetworkActivityIndicatorManager.h */,
				D4E8EDE3FD818BE08A2F6E2E569F91FE /* AFNetworkActivityIndicatorManager.m */,
				36EC8F11ACDEABCEB2222AEB3A491C9C /* UIActivityIndicatorView+AFNetworking.h */,
				881C9D882BFBDE5A0AE428735341A10D /* UIActivityIndicatorView+AFNetworking.m */,
				66496F623929C7C2499D28145BD854DF /* UIButton+AFNetworking.h */,
				89949E535DDCFD1B2E3F348C5E66D5D0 /* UIButton+AFNetworking.m */,
				EFDBE32B4F632386ADCFB7C67CE3127C /* UIImageView+AFNetworking.h */,
				7F395AA98529E36C8D4174333A163E6E /* UIImageView+AFNetworking.m */,
				353F0AB4A677201BC1E7513CA31C3973 /* UIKit+AFNetworking.h */,
				2E3E82FE5D990551C65481EC8BFECED7 /* UIProgressView+AFNetworking.h */,
				F51B7DF99A2A7D1F069B1136E8837A28 /* UIProgressView+AFNetworking.m */,
				82C1797D34FE2B9461D6169ED5510B46 /* UIRefreshControl+AFNetworking.h */,
				FE924FCF3297AB8F4131AC11BC0251AE /* UIRefreshControl+AFNetworking.m */,
				8BE20BF12CC78F8F55F45D8C1B067E44 /* WKWebView+AFNetworking.h */,
				2764AA8DABF8D76054F488460CD8F656 /* WKWebView+AFNetworking.m */,
			);
			name = UIKit;
			sourceTree = "<group>";
		};
		629D49A79044215018F4C4B2D46A15E8 /* Security */ = {
			isa = PBXGroup;
			children = (
				BE468D7759C7334EE74023716FD28832 /* AFSecurityPolicy.h */,
				9D1624E942265C016F7E082E47ECCBC8 /* AFSecurityPolicy.m */,
			);
			name = Security;
			sourceTree = "<group>";
		};
		C0834CEBB1379A84116EF29F93051C60 /* iOS */ = {
			isa = PBXGroup;
			children = (
				3212113385A8FBBDB272BD23C409FF61 /* Foundation.framework */,
			);
			name = iOS;
			sourceTree = "<group>";
		};
		C81D92CF7EA0D74210A1C8FDC1517942 /* NSURLSession */ = {
			isa = PBXGroup;
			children = (
				9610DE8C1A36299E2DFA9D7F51695EE6 /* AFCompatibilityMacros.h */,
				AB91561D44E0BB5BD8C81C06A3575EBD /* AFHTTPSessionManager.h */,
				6B9B7FAB8B749994510263464755D7F5 /* AFHTTPSessionManager.m */,
				8B2161DCD65B94F628389FC1CC116444 /* AFURLSessionManager.h */,
				A2C4AACC2EAF22AC21EEF34B9507B083 /* AFURLSessionManager.m */,
			);
			name = NSURLSession;
			sourceTree = "<group>";
		};
		CF1408CF629C7361332E53B88F7BD30C = {
			isa = PBXGroup;
			children = (
				9D940727FF8FB9C785EB98E56350EF41 /* Podfile */,
				D210D550F4EA176C3123ED886F8F87F5 /* Frameworks */,
				DCC8A56913F72BC1C6E315C28A6BDE10 /* Pods */,
				5776F2121431D14D43B46EC8E5298DAB /* Products */,
				3C126D8664812822351E5813A7D90761 /* Targets Support Files */,
			);
			sourceTree = "<group>";
		};
		D210D550F4EA176C3123ED886F8F87F5 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				C0834CEBB1379A84116EF29F93051C60 /* iOS */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		DCC8A56913F72BC1C6E315C28A6BDE10 /* Pods */ = {
			isa = PBXGroup;
			children = (
				F5E69EECB0AA2658AE3404D6010511EC /* AFNetworking */,
				518CE419F21E21E96FC6A42B44FFEA6C /* JSONModel */,
			);
			name = Pods;
			sourceTree = "<group>";
		};
		E619A11F6405EB0817E2F8038400C914 /* Serialization */ = {
			isa = PBXGroup;
			children = (
				99B400381F3ED939EC7E7433E45DC307 /* AFURLRequestSerialization.h */,
				DA5965686232FFA5AE624DD0C3E09049 /* AFURLRequestSerialization.m */,
				5BBBBFB84AB4FD6A1944FE71B5FFC7E0 /* AFURLResponseSerialization.h */,
				D1D5AABD355B0DD1C49D1335D9D55341 /* AFURLResponseSerialization.m */,
			);
			name = Serialization;
			sourceTree = "<group>";
		};
		F5E69EECB0AA2658AE3404D6010511EC /* AFNetworking */ = {
			isa = PBXGroup;
			children = (
				9657C0E560736F937697C3F260ECA79A /* AFNetworking.h */,
				C81D92CF7EA0D74210A1C8FDC1517942 /* NSURLSession */,
				537D48154C5C5047DF92C5345CAEB149 /* Reachability */,
				629D49A79044215018F4C4B2D46A15E8 /* Security */,
				E619A11F6405EB0817E2F8038400C914 /* Serialization */,
				5A38CA38A681531682D8F4F2BE03C2C3 /* Support Files */,
				5B9AEBCF98B6A6BC086B6A965CEE097C /* UIKit */,
			);
			name = AFNetworking;
			path = AFNetworking;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		1785918AE5415993C1221A913AF431C4 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				110BDB27F35AB2C27F0B19E2E3B30B77 /* Pods-captcha_oc-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B9ECBAE3144164158FDC62D90C3CEFDB /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6EE482EF823C5090378A600998A120F9 /* AFAutoPurgingImageCache.h in Headers */,
				F4352388490E70C1CF187D93E390FB2A /* AFCompatibilityMacros.h in Headers */,
				C9F701A2E6208D9E840E7A91287F21B9 /* AFHTTPSessionManager.h in Headers */,
				B4658F8495B18BD4EB5D893CAFF3BBBC /* AFImageDownloader.h in Headers */,
				722EC0191A173ECB2121618467D32DF4 /* AFNetworkActivityIndicatorManager.h in Headers */,
				08D5C0606D6FBFB4877FC3C3FF4A2208 /* AFNetworking-umbrella.h in Headers */,
				D66716504D475B6377F7DD5C7C2BBCDE /* AFNetworking.h in Headers */,
				163B07BE98EAA270AF0A7AA82F84208E /* AFNetworkReachabilityManager.h in Headers */,
				AECFCA2598AABAEB044F6C4594F3222F /* AFSecurityPolicy.h in Headers */,
				2657AB4F1D04803DABFE7DDF80C0EF7F /* AFURLRequestSerialization.h in Headers */,
				8D060BB549FA8C46C8A5E230C7E00CFE /* AFURLResponseSerialization.h in Headers */,
				A7E6C236F5C97B35484B553454B609B0 /* AFURLSessionManager.h in Headers */,
				E8EB9F2CFC54AFE86B618DB528647311 /* UIActivityIndicatorView+AFNetworking.h in Headers */,
				40426F061CD9B0B435C690CAFEA2BD00 /* UIButton+AFNetworking.h in Headers */,
				F191431A32F803DC12F12EF3B4D984BD /* UIImageView+AFNetworking.h in Headers */,
				4FCA4E8703DBF3D3FB7D5BA06229159D /* UIKit+AFNetworking.h in Headers */,
				3CA0C9185ED4469D427AA53B26E1B18F /* UIProgressView+AFNetworking.h in Headers */,
				2AB5C7D99BD529942B2C81AB0CD8A600 /* UIRefreshControl+AFNetworking.h in Headers */,
				07E08C56386C02093F27CF28E4D01F11 /* WKWebView+AFNetworking.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C10AE64316B9C31B3BB6170AF9933749 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				3AEA2F22B88CD78E671F25B60DB66ADA /* JSONAPI.h in Headers */,
				1071D84B6431759A11FAEBAAE29E3F32 /* JSONHTTPClient.h in Headers */,
				C7850AB3F7B5FDCA4F1F47F1EC205276 /* JSONKeyMapper.h in Headers */,
				191126E8D5A1E26339D2B767EF3AE197 /* JSONModel+networking.h in Headers */,
				DFF8D80125C062F6646E6EE798E6ABA3 /* JSONModel-umbrella.h in Headers */,
				75C939C90205A780AB001C84A0B90C24 /* JSONModel.h in Headers */,
				D358BCEB69D2E10B9BC6504C93D7D14C /* JSONModelClassProperty.h in Headers */,
				02E2954BD351E7B167D8F60B13208DAF /* JSONModelError.h in Headers */,
				C25BD96E691172D62B98B2F5F8D7F0C7 /* JSONModelLib.h in Headers */,
				E3886BA871A93B03E984D8BD7823D947 /* JSONValueTransformer.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		0130B3724283586C0E9D2A112D4F2AA1 /* AFNetworking */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = D4A4B40CB38ADC88749D1E1C8A945364 /* Build configuration list for PBXNativeTarget "AFNetworking" */;
			buildPhases = (
				B9ECBAE3144164158FDC62D90C3CEFDB /* Headers */,
				6F66130117A5CA2EFE69F1739F512499 /* Sources */,
				8F9BCCCF510F65077210B8D9944565EC /* Frameworks */,
				5AAFF4E9A7FD5CC955CC94BDE27FBA95 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = AFNetworking;
			productName = AFNetworking;
			productReference = A4FA15D44DF6BAC7550EDEED10862AA3 /* AFNetworking.framework */;
			productType = "com.apple.product-type.framework";
		};
		CA460CDEECCA9FE4C2D43FEC0DD0274D /* Pods-captcha_oc */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = FAA6F932656D859F9C1E23D716F36815 /* Build configuration list for PBXNativeTarget "Pods-captcha_oc" */;
			buildPhases = (
				1785918AE5415993C1221A913AF431C4 /* Headers */,
				93B57F07BA44DC0BD2CD7D273E2D2DF5 /* Sources */,
				E1547060E5BB5BC0AA9A9658E356C04B /* Frameworks */,
				D6F78274D9CD69A0B4E43E0678484DBD /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				E5B6908176DC9D3B144838A3C59B65EF /* PBXTargetDependency */,
				9808F207C6DAC9F3CB69A437067B8D06 /* PBXTargetDependency */,
			);
			name = "Pods-captcha_oc";
			productName = "Pods-captcha_oc";
			productReference = CEC9277B64020F21644359C5EEF15D00 /* Pods_captcha_oc.framework */;
			productType = "com.apple.product-type.framework";
		};
		E0EAF4371C6E4B30A1779D513CB4355B /* JSONModel */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 864246C4794538189B6310F839B9CD50 /* Build configuration list for PBXNativeTarget "JSONModel" */;
			buildPhases = (
				C10AE64316B9C31B3BB6170AF9933749 /* Headers */,
				9110B95813C4D6328D3D0802138CFDE0 /* Sources */,
				886E050E05F79FD43F2409BED7E67F73 /* Frameworks */,
				33431436401EBC85BE0A907D9728DA1C /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = JSONModel;
			productName = JSONModel;
			productReference = BAACA1BF7AB3DD28402C4F9A7C2FD104 /* JSONModel.framework */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		BFDFE7DC352907FC980B868725387E98 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1100;
				LastUpgradeCheck = 1100;
			};
			buildConfigurationList = 4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = CF1408CF629C7361332E53B88F7BD30C;
			productRefGroup = 5776F2121431D14D43B46EC8E5298DAB /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				0130B3724283586C0E9D2A112D4F2AA1 /* AFNetworking */,
				E0EAF4371C6E4B30A1779D513CB4355B /* JSONModel */,
				CA460CDEECCA9FE4C2D43FEC0DD0274D /* Pods-captcha_oc */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		33431436401EBC85BE0A907D9728DA1C /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5AAFF4E9A7FD5CC955CC94BDE27FBA95 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D6F78274D9CD69A0B4E43E0678484DBD /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		6F66130117A5CA2EFE69F1739F512499 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4992259A954F174FA8BDB394DB1C8F93 /* AFAutoPurgingImageCache.m in Sources */,
				8CE480485EFF2F15B7A394C90CFF274A /* AFHTTPSessionManager.m in Sources */,
				B83B1CA17424975882DA1066536E112B /* AFImageDownloader.m in Sources */,
				9D3A67B86C6A3A1339783953B3E833B9 /* AFNetworkActivityIndicatorManager.m in Sources */,
				3EB561D79F76C06AA538872D6302D35D /* AFNetworking-dummy.m in Sources */,
				2E7446308877A2FA6F48A7E545FFB1B1 /* AFNetworkReachabilityManager.m in Sources */,
				A6DE5A1B478949FE0444318D47C1A9CC /* AFSecurityPolicy.m in Sources */,
				BBFB85B525E1E77FCB19C84D911205D8 /* AFURLRequestSerialization.m in Sources */,
				828919193B61FC103B8846F54EF3131C /* AFURLResponseSerialization.m in Sources */,
				76536A816A576F08FADD3366A2E40094 /* AFURLSessionManager.m in Sources */,
				FDA522B328C907FBF9B11C64C1A7C624 /* UIActivityIndicatorView+AFNetworking.m in Sources */,
				38B57953D8705FA2092852071CD3A462 /* UIButton+AFNetworking.m in Sources */,
				232B2E8F32708EF2D85000B997FB69A2 /* UIImageView+AFNetworking.m in Sources */,
				02937E01710489D6668D0C3700E8EADA /* UIProgressView+AFNetworking.m in Sources */,
				01BEB06B4E262DCEA7A94F4206311BF9 /* UIRefreshControl+AFNetworking.m in Sources */,
				951B6669114E3C8D2E4961AFE2BB6647 /* WKWebView+AFNetworking.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9110B95813C4D6328D3D0802138CFDE0 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				C65CBBF120A2DD097498BFA38119D466 /* JSONAPI.m in Sources */,
				B4C7BC969594EB3E9323674B7FF7C66E /* JSONHTTPClient.m in Sources */,
				26A3D3037A05E7E99AFF098D11427775 /* JSONKeyMapper.m in Sources */,
				CFD38BDDB48F8D35B9A858E957A933A2 /* JSONModel+networking.m in Sources */,
				F442593E37B8F49398FDB1974EDF3FD6 /* JSONModel-dummy.m in Sources */,
				6FCDE0D44543F4DABF90A88E056D8199 /* JSONModel.m in Sources */,
				C64C8DE746D376EEA160794275302677 /* JSONModelClassProperty.m in Sources */,
				A52DD788A5384665F425D6A2C7493AD0 /* JSONModelError.m in Sources */,
				B0BA1C31A2A3AB25BC95A4B2DE0A7B5D /* JSONValueTransformer.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		93B57F07BA44DC0BD2CD7D273E2D2DF5 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4908757791D70EC3E10CFD5D74A3D7DB /* Pods-captcha_oc-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		9808F207C6DAC9F3CB69A437067B8D06 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = JSONModel;
			target = E0EAF4371C6E4B30A1779D513CB4355B /* JSONModel */;
			targetProxy = A7EE010AC3B25F0F77487482301479C4 /* PBXContainerItemProxy */;
		};
		E5B6908176DC9D3B144838A3C59B65EF /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = AFNetworking;
			target = 0130B3724283586C0E9D2A112D4F2AA1 /* AFNetworking */;
			targetProxy = B676510069A0115B0757504FE153B6AD /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		5C880C5B6641131C37D349C22FA44AFC /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E33ED7ECA7EDB642F3677AF95B0B0321 /* JSONModel.debug.xcconfig */;
			buildSettings = {
				CODE_SIGN_IDENTITY = "";
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				GCC_PREFIX_HEADER = "Target Support Files/JSONModel/JSONModel-prefix.pch";
				INFOPLIST_FILE = "Target Support Files/JSONModel/JSONModel-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/JSONModel/JSONModel.modulemap";
				PRODUCT_MODULE_NAME = JSONModel;
				PRODUCT_NAME = JSONModel;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		807A6C8CD3611C611C3255D732F76B18 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 99F2EB107B1682A61E3A3B9153F28788 /* JSONModel.release.xcconfig */;
			buildSettings = {
				CODE_SIGN_IDENTITY = "";
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				GCC_PREFIX_HEADER = "Target Support Files/JSONModel/JSONModel-prefix.pch";
				INFOPLIST_FILE = "Target Support Files/JSONModel/JSONModel-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/JSONModel/JSONModel.modulemap";
				PRODUCT_MODULE_NAME = JSONModel;
				PRODUCT_NAME = JSONModel;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		84C58B518D43C3D8F8F5FC5FA93CDEB7 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F600698801612B6FC6389DE2801F6ECD /* AFNetworking.debug.xcconfig */;
			buildSettings = {
				CODE_SIGN_IDENTITY = "";
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				GCC_PREFIX_HEADER = "Target Support Files/AFNetworking/AFNetworking-prefix.pch";
				INFOPLIST_FILE = "Target Support Files/AFNetworking/AFNetworking-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/AFNetworking/AFNetworking.modulemap";
				PRODUCT_MODULE_NAME = AFNetworking;
				PRODUCT_NAME = AFNetworking;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		92A89C51A2B4FE3EBBA361DE12886FC3 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 444541C8D6027EAB54536560D35EE988 /* Pods-captcha_oc.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGN_IDENTITY = "";
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				INFOPLIST_FILE = "Target Support Files/Pods-captcha_oc/Pods-captcha_oc-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 13.5;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-captcha_oc/Pods-captcha_oc.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		BD3A69076C1B8ED0EB8EED8E9254F409 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 395B8C7688FE05E151B7E8877157D627 /* AFNetworking.release.xcconfig */;
			buildSettings = {
				CODE_SIGN_IDENTITY = "";
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				GCC_PREFIX_HEADER = "Target Support Files/AFNetworking/AFNetworking-prefix.pch";
				INFOPLIST_FILE = "Target Support Files/AFNetworking/AFNetworking-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/AFNetworking/AFNetworking.modulemap";
				PRODUCT_MODULE_NAME = AFNetworking;
				PRODUCT_NAME = AFNetworking;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		DFD9D19FEEBF98DE271D7FBABCB845B5 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_DEBUG=1",
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.5;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Debug;
		};
		F5F04600F5626DC980F02790D1ED2C19 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_RELEASE=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.5;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Release;
		};
		F77E00ABB8F2CB35FA646A5854C06970 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 23B1B9E4464A77FA740351C0113202A1 /* Pods-captcha_oc.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGN_IDENTITY = "";
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				INFOPLIST_FILE = "Target Support Files/Pods-captcha_oc/Pods-captcha_oc-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 13.5;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-captcha_oc/Pods-captcha_oc.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DFD9D19FEEBF98DE271D7FBABCB845B5 /* Debug */,
				F5F04600F5626DC980F02790D1ED2C19 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		864246C4794538189B6310F839B9CD50 /* Build configuration list for PBXNativeTarget "JSONModel" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				5C880C5B6641131C37D349C22FA44AFC /* Debug */,
				807A6C8CD3611C611C3255D732F76B18 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		D4A4B40CB38ADC88749D1E1C8A945364 /* Build configuration list for PBXNativeTarget "AFNetworking" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				84C58B518D43C3D8F8F5FC5FA93CDEB7 /* Debug */,
				BD3A69076C1B8ED0EB8EED8E9254F409 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		FAA6F932656D859F9C1E23D716F36815 /* Build configuration list for PBXNativeTarget "Pods-captcha_oc" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				92A89C51A2B4FE3EBBA361DE12886FC3 /* Debug */,
				F77E00ABB8F2CB35FA646A5854C06970 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = BFDFE7DC352907FC980B868725387E98 /* Project object */;
}
