//
//  BaseJsonModel.m
//  captcha_oc
//
//  Created by kean_qi on 2020/5/23.
//  Copyright © 2020 kean_qi. All rights reserved.
//

#import "BaseJsonModel.h"

@implementation BaseJsonModel
+ (BOOL)propertyIsOptional:(NSString *)propertyName {
    return YES;
}

+ (J<PERSON><PERSON><PERSON>eyMapper *)keyMapper {
    return [[JSONKeyMapper alloc] initWithModelToJSONDictionary:@{@"id": @"ID",
    @"description": @"desc"
    }];
}

@end
