<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="16097" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="BYZ-38-t0r">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="16087"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="tne-QT-ifu">
            <objects>
                <viewController id="BYZ-38-t0r" customClass="ViewController" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="8bC-Xf-vdC">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" spacing="20" translatesAutoresizingMaskIntoConstraints="NO" id="4oN-6e-GC5">
                                <rect key="frame" x="20" y="144" width="374" height="40"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="账号" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="kCP-u2-TEe">
                                        <rect key="frame" x="0.0" y="0.0" width="35" height="40"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" borderStyle="roundedRect" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="Nhj-uD-kgL">
                                        <rect key="frame" x="55" y="0.0" width="319" height="40"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                        <textInputTraits key="textInputTraits"/>
                                    </textField>
                                </subviews>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="gX9-rB-NQF"/>
                                </constraints>
                            </stackView>
                            <stackView opaque="NO" contentMode="scaleToFill" spacing="20" translatesAutoresizingMaskIntoConstraints="NO" id="QoD-FU-aCt">
                                <rect key="frame" x="20" y="204" width="374" height="40"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="密码" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="9qU-fV-f8S">
                                        <rect key="frame" x="0.0" y="0.0" width="35" height="40"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" borderStyle="roundedRect" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="HB1-xD-NXS">
                                        <rect key="frame" x="55" y="0.0" width="319" height="40"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                        <textInputTraits key="textInputTraits"/>
                                    </textField>
                                </subviews>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="gkq-du-j8C"/>
                                </constraints>
                            </stackView>
                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="20" translatesAutoresizingMaskIntoConstraints="NO" id="Fc1-EW-e25">
                                <rect key="frame" x="60" y="264" width="294" height="30"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="249" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="bTK-nS-8ly">
                                        <rect key="frame" x="0.0" y="0.0" width="137" height="30"/>
                                        <color key="backgroundColor" red="0.8980392157" green="0.8980392157" blue="0.8980392157" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <state key="normal" title="滑动验证码"/>
                                        <connections>
                                            <action selector="buttonClick:" destination="BYZ-38-t0r" eventType="touchUpInside" id="Ve3-St-oKL"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" tag="1" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Gih-CJ-2xe">
                                        <rect key="frame" x="157" y="0.0" width="137" height="30"/>
                                        <color key="backgroundColor" red="0.8980392157" green="0.8980392157" blue="0.8980392157" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <state key="normal" title="点选验证码"/>
                                        <connections>
                                            <action selector="buttonClick:" destination="BYZ-38-t0r" eventType="touchUpInside" id="Aqm-3u-Q9j"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <constraints>
                                    <constraint firstAttribute="height" constant="30" id="2Sj-3U-lXt"/>
                                </constraints>
                            </stackView>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor" cocoaTouchSystemColor="whiteColor"/>
                        <constraints>
                            <constraint firstItem="6Tk-OE-BBY" firstAttribute="trailing" secondItem="4oN-6e-GC5" secondAttribute="trailing" constant="20" id="GMr-sR-UVO"/>
                            <constraint firstItem="6Tk-OE-BBY" firstAttribute="trailing" secondItem="QoD-FU-aCt" secondAttribute="trailing" constant="20" id="KlC-B6-df1"/>
                            <constraint firstItem="Fc1-EW-e25" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" constant="60" id="PaD-fc-exl"/>
                            <constraint firstItem="Fc1-EW-e25" firstAttribute="top" secondItem="QoD-FU-aCt" secondAttribute="bottom" constant="20" id="XuX-AK-j7B"/>
                            <constraint firstItem="QoD-FU-aCt" firstAttribute="top" secondItem="4oN-6e-GC5" secondAttribute="bottom" constant="20" id="ZrK-Yx-zQB"/>
                            <constraint firstItem="6Tk-OE-BBY" firstAttribute="trailing" secondItem="Fc1-EW-e25" secondAttribute="trailing" constant="60" id="bOU-Gu-QL8"/>
                            <constraint firstItem="4oN-6e-GC5" firstAttribute="top" secondItem="6Tk-OE-BBY" secondAttribute="top" constant="100" id="jFV-Oz-4he"/>
                            <constraint firstItem="QoD-FU-aCt" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" constant="20" id="qfv-4U-8GM"/>
                            <constraint firstItem="4oN-6e-GC5" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" constant="20" id="wrF-22-bcu"/>
                        </constraints>
                        <viewLayoutGuide key="safeArea" id="6Tk-OE-BBY"/>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="dkx-z0-nzr" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="137.68115942028987" y="118.52678571428571"/>
        </scene>
    </scenes>
</document>
