{"name": "angular2", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@angular/animations": "~11.0.1", "@angular/common": "~11.0.1", "@angular/compiler": "~11.0.1", "@angular/core": "~11.0.1", "@angular/forms": "~11.0.1", "@angular/http": "^7.2.16", "@angular/platform-browser": "~11.0.1", "@angular/platform-browser-dynamic": "~11.0.1", "@angular/router": "~11.0.1", "@types/crypto-js": "^4.0.1", "@types/jquery": "^3.5.4", "bootstrap": "^3.3.7", "crypto-js": "^4.0.0", "jquery": "^3.5.1", "rxjs": "~6.6.0", "tslib": "^2.0.0", "zone.js": "~0.10.2"}, "devDependencies": {"@angular-devkit/build-angular": "~0.1100.2", "@angular/cli": "~11.0.2", "@angular/compiler-cli": "~11.0.1", "@types/jasmine": "~3.6.0", "@types/node": "^12.11.1", "codelyzer": "^6.0.0", "jasmine-core": "~3.6.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~5.1.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.0.3", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "^1.5.0", "protractor": "~7.0.0", "ts-node": "~8.3.0", "tslint": "~6.1.0", "typescript": "~4.0.2"}}