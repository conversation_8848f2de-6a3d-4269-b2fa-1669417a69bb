{"description": "项目配置文件", "packOptions": {"ignore": []}, "setting": {"urlCheck": false, "es6": true, "enhance": false, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": false, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "bundle": false, "compileHotReLoad": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "enableEngineNative": false, "useIsolateContext": true, "useCompilerModule": true, "userConfirmedUseCompilerModuleSwitch": false, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true}, "compileType": "miniprogram", "libVersion": "2.16.0", "appid": "wxdb2c7ac2f09808bd", "projectname": "wx-applet-<PERSON>-<PERSON><PERSON>", "debugOptions": {"hidedInDevtools": []}, "scripts": {}, "staticServerOptions": {"baseURL": "", "servePath": ""}, "isGameTourist": false, "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {"search": {"list": []}, "conversation": {"list": []}, "game": {"list": []}, "plugin": {"list": []}, "gamePlugin": {"list": []}, "miniprogram": {"list": []}}}