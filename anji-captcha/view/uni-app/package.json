{"name": "my-vx", "version": "0.1.0", "private": true, "scripts": {"serve": "npm run dev:h5", "build": "npm run build:h5", "build:app-plus": "cross-env NODE_ENV=production UNI_PLATFORM=app-plus vue-cli-service uni-build", "build:custom": "cross-env NODE_ENV=production uniapp-cli custom", "build:h5": "cross-env NODE_ENV=production UNI_PLATFORM=h5 vue-cli-service uni-build", "build:mp-alipay": "cross-env NODE_ENV=production UNI_PLATFORM=mp-alipay vue-cli-service uni-build", "build:mp-baidu": "cross-env NODE_ENV=production UNI_PLATFORM=mp-baidu vue-cli-service uni-build", "build:mp-qq": "cross-env NODE_ENV=production UNI_PLATFORM=mp-qq vue-cli-service uni-build", "build:mp-toutiao": "cross-env NODE_ENV=production UNI_PLATFORM=mp-toutiao vue-cli-service uni-build", "build:mp-weixin": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build", "dev:app-plus": "cross-env NODE_ENV=development UNI_PLATFORM=app-plus vue-cli-service uni-build --watch", "dev:custom": "cross-env NODE_ENV=development uniapp-cli custom", "dev:h5": "cross-env NODE_ENV=development UNI_PLATFORM=h5 vue-cli-service uni-serve", "dev:mp-alipay": "cross-env NODE_ENV=development UNI_PLATFORM=mp-alipay vue-cli-service uni-build --watch", "dev:mp-baidu": "cross-env NODE_ENV=development UNI_PLATFORM=mp-baidu vue-cli-service uni-build --watch", "dev:mp-qq": "cross-env NODE_ENV=development UNI_PLATFORM=mp-qq vue-cli-service uni-build --watch", "dev:mp-toutiao": "cross-env NODE_ENV=development UNI_PLATFORM=mp-toutiao vue-cli-service uni-build --watch", "dev:mp-weixin": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch", "info": "node node_modules/@dcloudio/vue-cli-plugin-uni/commands/info.js"}, "dependencies": {"@dcloudio/uni-app-plus": "^2.0.0-26920200421006", "@dcloudio/uni-h5": "^2.0.0-26920200421006", "@dcloudio/uni-helper-json": "*", "@dcloudio/uni-mp-alipay": "^2.0.0-26920200421006", "@dcloudio/uni-mp-baidu": "^2.0.0-26920200421006", "@dcloudio/uni-mp-qq": "^2.0.0-26920200421006", "@dcloudio/uni-mp-toutiao": "^2.0.0-26920200421006", "@dcloudio/uni-mp-weixin": "^2.0.0-26920200421006", "@dcloudio/uni-stat": "^2.0.0-26920200421006", "crypto-js": "^4.0.0", "flyio": "^0.6.2", "js-md5": "^0.7.3", "regenerator-runtime": "^0.12.1", "vue": "^2.6.11", "vuex": "^3.3.0"}, "devDependencies": {"@dcloudio/uni-cli-shared": "^2.0.0-26920200421006", "@dcloudio/uni-migration": "^2.0.0-26920200421006", "@dcloudio/uni-template-compiler": "^2.0.0-26920200421006", "@dcloudio/vue-cli-plugin-hbuilderx": "^2.0.0-26920200421006", "@dcloudio/vue-cli-plugin-uni": "^2.0.0-26920200421006", "@dcloudio/vue-cli-plugin-uni-optimize": "^2.0.0-26920200421006", "@dcloudio/webpack-uni-mp-loader": "^2.0.0-26920200421006", "@dcloudio/webpack-uni-pages-loader": "^2.0.0-26920200421006", "@types/html5plus": "*", "@types/uni-app": "^1.4.2", "@vue/cli-plugin-babel": "3.5.1", "@vue/cli-service": "~4.2.0", "babel-plugin-import": "^1.11.0", "mini-types": "*", "miniprogram-api-typings": "^2.10.4", "postcss-comment": "^2.0.0", "vue-template-compiler": "^2.6.11"}, "browserslist": ["Android >= 4", "ios >= 8"], "uni-app": {"scripts": {}}}