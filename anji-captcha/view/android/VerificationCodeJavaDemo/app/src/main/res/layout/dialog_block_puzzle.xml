<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="330dp"
        android:layout_height="50dp"
        android:orientation="horizontal"
        android:paddingLeft="10dp"
        android:paddingRight="10dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:text="请完成安全验证"
            android:textColor="@android:color/black"
            android:textSize="18sp" />

        <TextView
            android:id="@+id/tv_delete"
            android:layout_width="27dp"
            android:layout_height="27dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="10dp"
            android:background="@drawable/et_delete" />
    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@android:color/darker_gray" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="10dp">

        <com.example.verificationcodejavademo.widget.DragImageView
            android:id="@+id/dragView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center" />

        <ImageView
            android:id="@+id/tv_refresh"
            android:layout_width="45dp"
            android:layout_height="45dp"
            android:layout_gravity="right|top"
            android:paddingLeft="5dp"
            android:paddingTop="5dp"
            android:paddingRight="15dp"
            android:paddingBottom="5dp"
            android:src="@drawable/icon_refresh" />

    </FrameLayout>

</LinearLayout>