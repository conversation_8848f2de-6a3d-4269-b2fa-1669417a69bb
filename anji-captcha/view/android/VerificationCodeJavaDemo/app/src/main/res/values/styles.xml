<resources>

    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>

    <style name="dialog" parent="@android:style/Theme.Dialog">

        <item name="android:windowFrame">@null</item>

        <item name="android:windowIsFloating">true</item>

        <item name="android:windowIsTranslucent">true</item>

        <item name="android:windowNoTitle">true</item>

        <item name="android:background">@android:color/transparent</item>

        <item name="android:windowBackground">@android:color/transparent</item>

        <item name="android:backgroundDimEnabled">true</item>

        <item name="android:backgroundDimAmount">0.6</item>

    </style>

</resources>
